/**
 * TypeScript interfaces for Checklist component
 * Defines props, state, and data structures for gift planning checklist functionality
 */

import { IChecklistItem } from '../../types/api';

// ============================================================================
// COMPONENT PROPS INTERFACES
// ============================================================================

export interface IChecklistProps {
  /** The event ID to load checklist items for */
  eventId: string;
  
  /** Optional callback when checklist items are updated */
  onItemsChange?: (items: IChecklistItem[]) => void;
  
  /** Optional callback when an item is added */
  onItemAdd?: (item: IChecklistItem) => void;
  
  /** Optional callback when an item is updated */
  onItemUpdate?: (item: IChecklistItem) => void;
  
  /** Optional callback when an item is deleted */
  onItemDelete?: (itemId: string) => void;
  
  /** Whether the checklist is in a loading state */
  isLoading?: boolean;
  
  /** Optional error message to display */
  error?: string;
  
  /** Optional CSS class name for styling */
  className?: string;
  
  /** Whether to show the add new item form */
  showAddForm?: boolean;
  
  /** Whether items can be edited inline */
  allowInlineEdit?: boolean;
  
  /** Whether items can be deleted */
  allowDelete?: boolean;
  
  /** Maximum number of items allowed */
  maxItems?: number;
  
  /** Whether to show completed items */
  showCompleted?: boolean;
  
  /** Whether to enable drag and drop reordering */
  enableReordering?: boolean;
}

// ============================================================================
// COMPONENT STATE INTERFACES
// ============================================================================

export interface IChecklistState {
  /** Array of checklist items */
  items: IChecklistItem[];
  
  /** Whether the component is loading data */
  isLoading: boolean;
  
  /** Current error message, if any */
  error: string | null;
  
  /** Whether the add new item form is visible */
  isAddFormVisible: boolean;
  
  /** ID of the item currently being edited */
  editingItemId: string | null;
  
  /** Temporary value for the item being edited */
  editingValue: string;
  
  /** New item text being typed */
  newItemText: string;
  
  /** Whether an operation is in progress */
  isProcessing: boolean;
  
  /** Filter for showing items */
  filter: ChecklistFilter;
  
  /** Items that are being optimistically updated */
  optimisticUpdates: Map<string, IChecklistItem>;
}

// ============================================================================
// ITEM INTERFACES
// ============================================================================

export interface IChecklistItemDisplay extends IChecklistItem {
  /** Whether this item is being edited */
  isEditing: boolean;
  
  /** Whether this item is being processed */
  isProcessing: boolean;
  
  /** Whether this item has an optimistic update */
  hasOptimisticUpdate: boolean;
  
  /** Display index for ordering */
  displayIndex: number;
}

export interface INewChecklistItem {
  /** The text content of the item */
  item: string;
  
  /** The event ID this item belongs to */
  event_id: string;
  
  /** Initial status (always 'pending' for new items) */
  status: 'pending';
}

// ============================================================================
// ACTION INTERFACES
// ============================================================================

export interface IChecklistAction {
  id: string;
  type: ChecklistActionType;
  label: string;
  icon?: string;
  variant: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  onClick: () => void;
}

export interface IChecklistItemAction {
  id: string;
  type: ChecklistItemActionType;
  label: string;
  icon?: string;
  disabled?: boolean;
  onClick: (item: IChecklistItem) => void;
}

// ============================================================================
// FORM INTERFACES
// ============================================================================

export interface IAddItemFormData {
  text: string;
  isValid: boolean;
  error?: string;
}

export interface IEditItemFormData {
  text: string;
  isValid: boolean;
  error?: string;
  originalText: string;
}

// ============================================================================
// CONSTANTS AND DEFAULTS
// ============================================================================

export const DEFAULT_CHECKLIST_STATE: IChecklistState = {
  items: [],
  isLoading: false,
  error: null,
  isAddFormVisible: false,
  editingItemId: null,
  editingValue: '',
  newItemText: '',
  isProcessing: false,
  filter: 'all',
  optimisticUpdates: new Map(),
};

export const CHECKLIST_MESSAGES = {
  LOADING: 'Loading checklist items...',
  ERROR_GENERIC: 'An error occurred while managing the checklist',
  ERROR_NETWORK: 'Network error. Please check your connection and try again.',
  ERROR_LOAD: 'Failed to load checklist items',
  ERROR_ADD: 'Failed to add checklist item',
  ERROR_UPDATE: 'Failed to update checklist item',
  ERROR_DELETE: 'Failed to delete checklist item',
  ERROR_EMPTY_ITEM: 'Item text cannot be empty',
  ERROR_DUPLICATE_ITEM: 'This item already exists in the checklist',
  ERROR_MAX_ITEMS: 'Maximum number of items reached',
  SUCCESS_ADD: 'Item added successfully',
  SUCCESS_UPDATE: 'Item updated successfully',
  SUCCESS_DELETE: 'Item deleted successfully',
  CONFIRM_DELETE: 'Are you sure you want to delete this item?',
  CONFIRM_CLEAR_COMPLETED: 'Are you sure you want to clear all completed items?',
  EMPTY_STATE_TITLE: 'No checklist items yet',
  EMPTY_STATE_MESSAGE: 'Add your first gift planning item to get started.',
  EMPTY_FILTERED_TITLE: 'No items match your filter',
  EMPTY_FILTERED_MESSAGE: 'Try changing the filter or add new items.',
  ADD_ITEM_PLACEHOLDER: 'Enter gift planning item...',
  ADD_ITEM_BUTTON: 'Add Item',
  EDIT_ITEM_SAVE: 'Save',
  EDIT_ITEM_CANCEL: 'Cancel',
  FILTER_ALL: 'All Items',
  FILTER_PENDING: 'Pending',
  FILTER_COMPLETED: 'Completed',
  ARIA_ADD_ITEM: 'Add new checklist item',
  ARIA_EDIT_ITEM: 'Edit checklist item',
  ARIA_DELETE_ITEM: 'Delete checklist item',
  ARIA_TOGGLE_ITEM: 'Toggle item completion status',
  ARIA_FILTER: 'Filter checklist items',
  ARIA_CLEAR_COMPLETED: 'Clear all completed items',
} as const;

export const DEFAULT_MAX_ITEMS = 50;
export const MAX_ITEM_LENGTH = 200;
export const MIN_ITEM_LENGTH = 1;

// ============================================================================
// VALIDATION AND HELPER TYPES
// ============================================================================

export type ChecklistFilter = 'all' | 'pending' | 'completed';

export type ChecklistActionType = 
  | 'add_item'
  | 'clear_completed'
  | 'refresh'
  | 'toggle_filter';

export type ChecklistItemActionType = 
  | 'toggle_status'
  | 'edit'
  | 'delete'
  | 'move_up'
  | 'move_down';

export type ChecklistError = 
  | 'LOAD_ERROR'
  | 'ADD_ERROR'
  | 'UPDATE_ERROR'
  | 'DELETE_ERROR'
  | 'NETWORK_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

// ============================================================================
// UTILITY TYPE GUARDS AND VALIDATORS
// ============================================================================

export const isValidItemText = (text: string): boolean => {
  return typeof text === 'string' && 
         text.trim().length >= MIN_ITEM_LENGTH && 
         text.trim().length <= MAX_ITEM_LENGTH;
};

export const isValidEventId = (eventId: string): boolean => {
  return typeof eventId === 'string' && eventId.trim().length > 0;
};

export const isValidChecklistItem = (item: any): item is IChecklistItem => {
  return (
    typeof item === 'object' &&
    item !== null &&
    typeof item.list_id === 'string' &&
    typeof item.event_id === 'string' &&
    typeof item.item === 'string' &&
    (item.status === 'pending' || item.status === 'done') &&
    isValidItemText(item.item)
  );
};

export const isDuplicateItem = (items: IChecklistItem[], newText: string): boolean => {
  const normalizedNewText = newText.trim().toLowerCase();
  return items.some(item => item.item.trim().toLowerCase() === normalizedNewText);
};

export const filterItems = (items: IChecklistItem[], filter: ChecklistFilter): IChecklistItem[] => {
  switch (filter) {
    case 'pending':
      return items.filter(item => item.status === 'pending');
    case 'completed':
      return items.filter(item => item.status === 'done');
    case 'all':
    default:
      return items;
  }
};

export const getItemStats = (items: IChecklistItem[]) => {
  const total = items.length;
  const completed = items.filter(item => item.status === 'done').length;
  const pending = total - completed;
  const completionPercentage = total > 0 ? Math.round((completed / total) * 100) : 0;
  
  return {
    total,
    completed,
    pending,
    completionPercentage,
  };
};
