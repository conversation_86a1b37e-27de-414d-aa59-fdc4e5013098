/**
 * ReminderCard Component Tests
 * Comprehensive test suite for ReminderCard component functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReminderCard } from './ReminderCard';
import { IContact } from '../../types/api';
import { REMINDER_CARD_MESSAGES } from './types';

// Mock date-fns to ensure consistent test results
jest.mock('date-fns', () => {
  const actualDateFns = jest.requireActual('date-fns');
  return {
    ...actualDateFns,
    parseISO: jest.fn((dateString: string) => new Date(dateString)),
    format: jest.fn((date: Date, formatString: string) => {
      if (formatString === 'MMM dd, yyyy') {
        return 'Dec 25, 2025';
      }
      return '2025-12-18';
    }),
    differenceInDays: jest.fn(() => 164), // Mock consistent difference
    addDays: jest.fn((date: Date, days: number) => new Date('2025-12-18')),
    isPast: jest.fn(() => false),
  };
});

// Sample test data
const mockEvent: IContact = {
  id: '1',
  contact_name: 'John Doe',
  contact_phone: '+1234567890',
  event_date: '2025-12-25',
  lead_time_days: 7,
  reminder_sent: false,
};

const mockEventSent: IContact = {
  ...mockEvent,
  id: '2',
  reminder_sent: true,
};

const mockEventOverdue: IContact = {
  ...mockEvent,
  id: '3',
  event_date: '2025-07-10', // Past date
};

// Mock handlers
const mockHandlers = {
  onReminderSent: jest.fn(),
  onSnooze: jest.fn(),
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onShowChecklist: jest.fn(),
};

describe('ReminderCard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock current date to ensure consistent tests
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2025-07-14'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  // ============================================================================
  // BASIC RENDERING TESTS
  // ============================================================================

  describe('Basic Rendering', () => {
    it('renders reminder card with event details', () => {
      render(<ReminderCard event={mockEvent} />);
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('+1234567890')).toBeInTheDocument();
      expect(screen.getByText(/Dec 25, 2025/)).toBeInTheDocument();
    });

    it('renders with correct ARIA labels', () => {
      render(<ReminderCard event={mockEvent} />);
      
      const card = screen.getByRole('article');
      expect(card).toHaveAttribute('aria-label', expect.stringContaining('John Doe'));
    });

    it('applies correct CSS classes based on props', () => {
      const { rerender } = render(
        <ReminderCard 
          event={mockEvent} 
          className="custom-class"
          isSelected={true}
        />
      );
      
      const card = screen.getByRole('article');
      expect(card).toHaveClass('reminder-card', 'custom-class', 'reminder-card--selected');
      
      rerender(<ReminderCard event={mockEvent} isLoading={true} />);
      expect(card).toHaveClass('reminder-card--loading');
    });
  });

  // ============================================================================
  // STATUS DISPLAY TESTS
  // ============================================================================

  describe('Status Display', () => {
    it('shows pending status for future reminders', () => {
      render(<ReminderCard event={mockEvent} />);
      
      expect(screen.getByText(/Due in \d+ days/)).toBeInTheDocument();
    });

    it('shows sent status for completed reminders', () => {
      render(<ReminderCard event={mockEventSent} />);
      
      expect(screen.getByText('Reminder Sent')).toBeInTheDocument();
    });

    it('shows overdue status for past due reminders', () => {
      render(<ReminderCard event={mockEventOverdue} />);
      
      expect(screen.getByText('Overdue')).toBeInTheDocument();
    });

    it('applies correct status theme classes', () => {
      const { rerender } = render(<ReminderCard event={mockEvent} />);
      
      let card = screen.getByRole('article');
      expect(card).toHaveClass('reminder-card--pending');
      
      rerender(<ReminderCard event={mockEventSent} />);
      expect(card).toHaveClass('reminder-card--sent');
      
      rerender(<ReminderCard event={mockEventOverdue} />);
      expect(card).toHaveClass('reminder-card--overdue');
    });
  });

  // ============================================================================
  // ACTION BUTTON TESTS
  // ============================================================================

  describe('Action Buttons', () => {
    it('renders action buttons when showActions is true', () => {
      render(
        <ReminderCard 
          event={mockEvent} 
          showActions={true}
          {...mockHandlers}
        />
      );
      
      expect(screen.getByRole('button', { name: /mark reminder as sent/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /snooze reminder/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /edit reminder/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /delete reminder/i })).toBeInTheDocument();
    });

    it('hides action buttons when showActions is false', () => {
      render(
        <ReminderCard 
          event={mockEvent} 
          showActions={false}
          {...mockHandlers}
        />
      );
      
      expect(screen.queryByRole('button', { name: /mark reminder as sent/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /snooze reminder/i })).not.toBeInTheDocument();
    });

    it('hides mark sent and snooze buttons for sent reminders', () => {
      render(
        <ReminderCard 
          event={mockEventSent} 
          showActions={true}
          {...mockHandlers}
        />
      );
      
      expect(screen.queryByRole('button', { name: /mark reminder as sent/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /snooze reminder/i })).not.toBeInTheDocument();
      expect(screen.getByRole('button', { name: /edit reminder/i })).toBeInTheDocument();
    });

    it('calls onReminderSent when mark sent button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <ReminderCard 
          event={mockEvent} 
          onReminderSent={mockHandlers.onReminderSent}
        />
      );
      
      const markSentButton = screen.getByRole('button', { name: /mark reminder as sent/i });
      await user.click(markSentButton);
      
      expect(mockHandlers.onReminderSent).toHaveBeenCalledWith(mockEvent.id);
    });

    it('calls onEdit when edit button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <ReminderCard 
          event={mockEvent} 
          onEdit={mockHandlers.onEdit}
        />
      );
      
      const editButton = screen.getByRole('button', { name: /edit reminder/i });
      await user.click(editButton);
      
      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockEvent);
    });

    it('shows confirmation dialog before deleting', async () => {
      const user = userEvent.setup();
      
      // Mock window.confirm
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(true);
      
      render(
        <ReminderCard 
          event={mockEvent} 
          onDelete={mockHandlers.onDelete}
        />
      );
      
      const deleteButton = screen.getByRole('button', { name: /delete reminder/i });
      await user.click(deleteButton);
      
      expect(confirmSpy).toHaveBeenCalledWith(REMINDER_CARD_MESSAGES.CONFIRM_DELETE);
      expect(mockHandlers.onDelete).toHaveBeenCalledWith(mockEvent.id);
      
      confirmSpy.mockRestore();
    });

    it('does not delete when confirmation is cancelled', async () => {
      const user = userEvent.setup();
      
      // Mock window.confirm to return false
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(false);
      
      render(
        <ReminderCard 
          event={mockEvent} 
          onDelete={mockHandlers.onDelete}
        />
      );
      
      const deleteButton = screen.getByRole('button', { name: /delete reminder/i });
      await user.click(deleteButton);
      
      expect(confirmSpy).toHaveBeenCalled();
      expect(mockHandlers.onDelete).not.toHaveBeenCalled();
      
      confirmSpy.mockRestore();
    });
  });

  // ============================================================================
  // SNOOZE FUNCTIONALITY TESTS
  // ============================================================================

  describe('Snooze Functionality', () => {
    it('opens snooze dropdown when snooze button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <ReminderCard 
          event={mockEvent} 
          onSnooze={mockHandlers.onSnooze}
        />
      );
      
      const snoozeButton = screen.getByRole('button', { name: /snooze reminder/i });
      await user.click(snoozeButton);
      
      expect(screen.getByText('Snooze for:')).toBeInTheDocument();
      expect(screen.getByText('1 Hour')).toBeInTheDocument();
      expect(screen.getByText('1 Day')).toBeInTheDocument();
    });

  // ============================================================================
  // EXPAND/COLLAPSE FUNCTIONALITY TESTS
  // ============================================================================

  describe('Expand/Collapse Functionality', () => {
    it('toggles expanded state when expand button is clicked', async () => {
      const user = userEvent.setup();

      render(<ReminderCard event={mockEvent} />);

      const expandButton = screen.getByRole('button', { name: /expand reminder details/i });
      expect(expandButton).toHaveAttribute('aria-expanded', 'false');

      await user.click(expandButton);

      expect(expandButton).toHaveAttribute('aria-expanded', 'true');
      expect(screen.getByText('Reminder Date:')).toBeInTheDocument();
      expect(screen.getByText('Lead Time:')).toBeInTheDocument();
    });

    it('shows details when showDetails prop is true', () => {
      render(<ReminderCard event={mockEvent} showDetails={true} />);

      expect(screen.getByText('Reminder Date:')).toBeInTheDocument();
      expect(screen.getByText('Lead Time:')).toBeInTheDocument();
      expect(screen.getByText('7 days')).toBeInTheDocument();
    });

    it('shows event passed warning for past events', () => {
      const pastEvent = { ...mockEvent, event_date: '2025-07-01' };

      render(<ReminderCard event={pastEvent} showDetails={true} />);

      expect(screen.getByText('Event has passed')).toBeInTheDocument();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    it('displays external error message', () => {
      render(
        <ReminderCard
          event={mockEvent}
          error="Network connection failed"
        />
      );

      expect(screen.getByText('Network connection failed')).toBeInTheDocument();
      expect(screen.getByRole('alert')).toBeInTheDocument();
    });

    it('allows dismissing error message', async () => {
      const user = userEvent.setup();

      render(
        <ReminderCard
          event={mockEvent}
          error="Test error message"
        />
      );

      expect(screen.getByText('Test error message')).toBeInTheDocument();

      const dismissButton = screen.getByRole('button', { name: /dismiss error/i });
      await user.click(dismissButton);

      expect(screen.queryByText('Test error message')).not.toBeInTheDocument();
    });

    it('handles action errors gracefully', async () => {
      const user = userEvent.setup();
      const failingHandler = jest.fn().mockRejectedValue(new Error('Action failed'));

      render(
        <ReminderCard
          event={mockEvent}
          onReminderSent={failingHandler}
        />
      );

      const markSentButton = screen.getByRole('button', { name: /mark reminder as sent/i });
      await user.click(markSentButton);

      await waitFor(() => {
        expect(screen.getByText('Action failed')).toBeInTheDocument();
      });
    });
  });

  // ============================================================================
  // LOADING STATE TESTS
  // ============================================================================

  describe('Loading States', () => {
    it('shows loading overlay when isLoading is true', () => {
      render(<ReminderCard event={mockEvent} isLoading={true} />);

      expect(screen.getByText(REMINDER_CARD_MESSAGES.LOADING)).toBeInTheDocument();
      expect(screen.getByRole('article')).toHaveClass('reminder-card--loading');
    });

    it('disables action buttons during loading', () => {
      render(
        <ReminderCard
          event={mockEvent}
          isLoading={true}
          {...mockHandlers}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        if (button.getAttribute('aria-label') !== 'Expand reminder details') {
          expect(button).toBeDisabled();
        }
      });
    });

    it('shows loading state during action processing', async () => {
      const user = userEvent.setup();
      const slowHandler = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));

      render(
        <ReminderCard
          event={mockEvent}
          onReminderSent={slowHandler}
        />
      );

      const markSentButton = screen.getByRole('button', { name: /mark reminder as sent/i });
      await user.click(markSentButton);

      expect(screen.getByText(REMINDER_CARD_MESSAGES.LOADING)).toBeInTheDocument();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility', () => {
    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <ReminderCard
          event={mockEvent}
          {...mockHandlers}
        />
      );

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByRole('button', { name: /mark reminder as sent/i })).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /snooze reminder/i })).toHaveFocus();
    });

    it('has proper ARIA attributes', () => {
      render(<ReminderCard event={mockEvent} />);

      const card = screen.getByRole('article');
      expect(card).toHaveAttribute('aria-label');

      const expandButton = screen.getByRole('button', { name: /expand/i });
      expect(expandButton).toHaveAttribute('aria-expanded');
    });

    it('announces errors to screen readers', () => {
      render(
        <ReminderCard
          event={mockEvent}
          error="Test error"
        />
      );

      const errorElement = screen.getByRole('alert');
      expect(errorElement).toBeInTheDocument();
      expect(errorElement).toHaveTextContent('Test error');
    });
  });

  // ============================================================================
  // INTEGRATION TESTS
  // ============================================================================

  describe('Integration Tests', () => {
    it('handles complete user workflow', async () => {
      const user = userEvent.setup();

      render(
        <ReminderCard
          event={mockEvent}
          {...mockHandlers}
        />
      );

      // Expand details
      await user.click(screen.getByRole('button', { name: /expand/i }));
      expect(screen.getByText('Reminder Date:')).toBeInTheDocument();

      // Open snooze dropdown
      await user.click(screen.getByRole('button', { name: /snooze/i }));
      expect(screen.getByText('Snooze for:')).toBeInTheDocument();

      // Select snooze option
      await user.click(screen.getByText('4 Hours'));
      expect(mockHandlers.onSnooze).toHaveBeenCalledWith(mockEvent.id, 4);

      // Mark as sent
      await user.click(screen.getByRole('button', { name: /mark reminder as sent/i }));
      expect(mockHandlers.onReminderSent).toHaveBeenCalledWith(mockEvent.id);
    });

    it('maintains state consistency across interactions', async () => {
      const user = userEvent.setup();

      render(<ReminderCard event={mockEvent} />);

      const expandButton = screen.getByRole('button', { name: /expand/i });

      // Expand
      await user.click(expandButton);
      expect(expandButton).toHaveAttribute('aria-expanded', 'true');

      // Collapse
      await user.click(expandButton);
      expect(expandButton).toHaveAttribute('aria-expanded', 'false');
      expect(screen.queryByText('Reminder Date:')).not.toBeInTheDocument();
    });
  });
});

    it('calls onSnooze with correct hours when snooze option is selected', async () => {
      const user = userEvent.setup();
      
      render(
        <ReminderCard 
          event={mockEvent} 
          onSnooze={mockHandlers.onSnooze}
        />
      );
      
      // Open snooze dropdown
      const snoozeButton = screen.getByRole('button', { name: /snooze reminder/i });
      await user.click(snoozeButton);
      
      // Click on 1 Day option
      const oneDayOption = screen.getByText('1 Day');
      await user.click(oneDayOption);
      
      expect(mockHandlers.onSnooze).toHaveBeenCalledWith(mockEvent.id, 24);
    });

    it('closes snooze dropdown when close button is clicked', async () => {
      const user = userEvent.setup();
      
      render(
        <ReminderCard 
          event={mockEvent} 
          onSnooze={mockHandlers.onSnooze}
        />
      );
      
      // Open snooze dropdown
      const snoozeButton = screen.getByRole('button', { name: /snooze reminder/i });
      await user.click(snoozeButton);
      
      expect(screen.getByText('Snooze for:')).toBeInTheDocument();
      
      // Close dropdown
      const closeButton = screen.getByRole('button', { name: /close snooze options/i });
      await user.click(closeButton);
      
      expect(screen.queryByText('Snooze for:')).not.toBeInTheDocument();
    });
  });
