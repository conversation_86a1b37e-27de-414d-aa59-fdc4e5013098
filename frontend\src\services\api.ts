/**
 * API Service Layer for Reminder & Gifting MVP
 * Handles all communication with Google Apps Script backend
 *
 * Features:
 * - TypeScript interfaces for type safety
 * - Retry logic with exponential backoff
 * - Comprehensive error handling
 * - User-friendly error messages
 * - Request/response validation
 */

import {
  IContactData,
  IImportContactsRequest,
  ImportContactsResult,
  GetEventsResult,
  ScheduleRemindersResult,
  GetChecklistsResult,
  IApiConfig,
  DEFAULT_API_CONFIG,
  isApiError,
  isImportContactsSuccess,
  isGetEventsSuccess,
  isScheduleRemindersSuccess,
  isGetChecklistsSuccess,
} from '../types/api';

// ============================================================================
// API CLIENT CLASS
// ============================================================================

export class ApiClient {
  private config: IApiConfig;

  constructor(config: Partial<IApiConfig> = {}) {
    this.config = { ...DEFAULT_API_CONFIG, ...config };

    if (!this.config.baseUrl) {
      // eslint-disable-next-line no-console
      console.warn(
        'API base URL not configured. Set REACT_APP_API_URL environment variable.'
      );
    }
  }

  /**
   * Generic HTTP request method with retry logic
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    retryCount = 0
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`;

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        this.config.timeout
      );

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data as T;
    } catch (error) {
      // Retry logic with exponential backoff
      if (retryCount < this.config.retryAttempts) {
        const delay = this.config.retryDelay * Math.pow(2, retryCount);
        // eslint-disable-next-line no-console
        console.warn(
          `Request failed, retrying in ${delay}ms... (attempt ${retryCount + 1}/${this.config.retryAttempts})`
        );

        await new Promise(resolve => setTimeout(resolve, delay));
        return this.makeRequest<T>(endpoint, options, retryCount + 1);
      }

      // Transform error into user-friendly message
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(
            'Request timed out. Please check your internet connection and try again.'
          );
        }
        throw new Error(`Network error: ${error.message}`);
      }

      throw new Error('An unexpected error occurred. Please try again.');
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck(): Promise<string> {
    try {
      const response = await this.makeRequest<string>('/', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      throw new Error(
        `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  /**
   * Import contacts data
   */
  async importContacts(
    contactsData: IContactData[]
  ): Promise<{ success: true; importedCount: number }> {
    // Validate input data
    if (!Array.isArray(contactsData) || contactsData.length === 0) {
      throw new Error('Contact data must be a non-empty array');
    }

    // Validate each contact
    for (const contact of contactsData) {
      if (!contact.contact_name || typeof contact.contact_name !== 'string') {
        throw new Error('Contact name is required and must be a string');
      }
      if (!contact.contact_phone || typeof contact.contact_phone !== 'string') {
        throw new Error('Contact phone is required and must be a string');
      }
      if (!contact.event_date || typeof contact.event_date !== 'string') {
        throw new Error(
          'Event date is required and must be a string in YYYY-MM-DD format'
        );
      }
      if (
        typeof contact.lead_time_days !== 'number' ||
        contact.lead_time_days < 0
      ) {
        throw new Error('Lead time days must be a non-negative number');
      }
    }

    const requestBody: IImportContactsRequest = { data: contactsData };

    try {
      const response = await this.makeRequest<ImportContactsResult>(
        '/?action=importContacts',
        {
          method: 'POST',
          body: JSON.stringify(requestBody),
        }
      );

      if (isApiError(response)) {
        throw new Error(response.error.message || 'Failed to import contacts');
      }

      if (isImportContactsSuccess(response)) {
        return response;
      }

      throw new Error('Unexpected response format from server');
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to import contacts. Please try again.');
    }
  }

  /**
   * Get all events/reminders
   */
  async getEvents(): Promise<GetEventsResult> {
    try {
      const response = await this.makeRequest<GetEventsResult>(
        '/?action=getEvents',
        {
          method: 'GET',
        }
      );

      if (isApiError(response)) {
        throw new Error(response.error.message || 'Failed to fetch events');
      }

      if (isGetEventsSuccess(response)) {
        return response;
      }

      throw new Error('Unexpected response format from server');
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to fetch events. Please try again.');
    }
  }

  /**
   * Schedule due reminders
   */
  async scheduleReminders(): Promise<{ scheduledCount: number }> {
    try {
      const response = await this.makeRequest<ScheduleRemindersResult>(
        '/?action=scheduleReminders',
        {
          method: 'POST',
          body: JSON.stringify({}),
        }
      );

      if (isApiError(response)) {
        throw new Error(
          response.error.message || 'Failed to schedule reminders'
        );
      }

      if (isScheduleRemindersSuccess(response)) {
        return response;
      }

      throw new Error('Unexpected response format from server');
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to schedule reminders. Please try again.');
    }
  }

  /**
   * Get checklist items for a specific event
   */
  async getChecklists(eventId: string): Promise<GetChecklistsResult> {
    if (!eventId || typeof eventId !== 'string') {
      throw new Error('Event ID is required and must be a string');
    }

    try {
      const response = await this.makeRequest<GetChecklistsResult>(
        `/?action=getChecklists&eventId=${encodeURIComponent(eventId)}`,
        {
          method: 'GET',
        }
      );

      if (isApiError(response)) {
        throw new Error(response.error.message || 'Failed to fetch checklists');
      }

      if (isGetChecklistsSuccess(response)) {
        return response;
      }

      throw new Error('Unexpected response format from server');
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to fetch checklists. Please try again.');
    }
  }
}

// ============================================================================
// DEFAULT API CLIENT INSTANCE
// ============================================================================

export const apiClient = new ApiClient();

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

export const {
  healthCheck,
  importContacts,
  getEvents,
  scheduleReminders,
  getChecklists,
} = apiClient;
