/**
 * EventList Component Styles
 * Responsive design with mobile-first approach
 * Follows established design patterns from ImportForm
 */

/* ============================================================================
   MAIN CONTAINER
   ============================================================================ */

.event-list {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

/* ============================================================================
   HEADER SECTION
   ============================================================================ */

.event-list__header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.event-list__title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.event-list__count {
  font-size: 0.875rem;
  color: #718096;
  background-color: #f7fafc;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
}

/* ============================================================================
   CONTROLS SECTION
   ============================================================================ */

.event-list__controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.event-list__search {
  flex: 1;
  min-width: 200px;
}

.event-list__search-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.event-list__search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.event-list__filter {
  min-width: 150px;
}

.event-list__filter-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.15s ease-in-out;
}

.event-list__filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.event-list__refresh-button {
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.event-list__refresh-button:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.event-list__refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ============================================================================
   ERROR HANDLING
   ============================================================================ */

.event-list__error {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.375rem;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  color: #dc2626;
}

.event-list__error-message {
  flex: 1;
  font-size: 0.875rem;
}

.event-list__error-dismiss {
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  font-size: 1rem;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.15s ease-in-out;
}

.event-list__error-dismiss:hover {
  background-color: rgba(220, 38, 38, 0.1);
}

/* ============================================================================
   LOADING STATE
   ============================================================================ */

.event-list__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  color: #718096;
}

.event-list__loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.event-list__loading-message {
  font-size: 0.875rem;
}

/* ============================================================================
   EMPTY STATE
   ============================================================================ */

.event-list__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  color: #718096;
}

.event-list__empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.event-list__empty-message {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #4a5568;
}

.event-list__empty-hint {
  font-size: 0.875rem;
  color: #718096;
}

/* ============================================================================
   CONTENT SECTION
   ============================================================================ */

.event-list__content {
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
}

/* ============================================================================
   HEADER ROW
   ============================================================================ */

.event-list__header {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1.5fr;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e2e8f0;
  font-weight: 600;
  font-size: 0.875rem;
  color: #4a5568;
}

.event-list__sort-button {
  background: none;
  border: none;
  font-weight: 600;
  font-size: 0.875rem;
  color: #4a5568;
  cursor: pointer;
  text-align: left;
  padding: 0;
  transition: color 0.15s ease-in-out;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.event-list__sort-button:hover {
  color: #2d3748;
}

.event-list__header-status {
  color: #4a5568;
}

/* ============================================================================
   EVENT ITEMS
   ============================================================================ */

.event-list__items {
  max-height: 600px;
  overflow-y: auto;
}

.event-list__item {
  display: grid;
  grid-template-columns: 2fr 1.5fr 1fr 1.5fr;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  align-items: center;
}

.event-list__item:hover {
  background-color: #f8fafc;
}

.event-list__item:focus {
  outline: none;
  background-color: #f1f5f9;
  box-shadow: inset 0 0 0 2px #3b82f6;
}

.event-list__item--selected {
  background-color: #eff6ff;
  border-left: 4px solid #3b82f6;
}

.event-list__item:last-child {
  border-bottom: none;
}

/* Contact Information */
.event-list__item-contact {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.event-list__contact-name {
  font-weight: 500;
  color: #1a202c;
  font-size: 0.875rem;
}

.event-list__contact-phone {
  font-size: 0.75rem;
  color: #718096;
  font-family: 'Courier New', monospace;
}

/* Event Date Information */
.event-list__item-date {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.event-list__event-date {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.875rem;
}

.event-list__days-until {
  font-size: 0.75rem;
  color: #718096;
}

/* Lead Time */
.event-list__item-lead-time {
  font-size: 0.875rem;
  color: #4a5568;
  text-align: center;
}

/* Status Information */
.event-list__item-status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.event-list__status--pending {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

.event-list__status--due-today {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fca5a5;
}

.event-list__status--overdue {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #f87171;
}

.event-list__status--sent {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #6ee7b7;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  .event-list {
    padding: 0.5rem;
  }

  .event-list__controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .event-list__search {
    min-width: unset;
  }

  .event-list__header,
  .event-list__item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .event-list__header {
    display: none; /* Hide header on mobile, use card layout */
  }

  .event-list__item {
    display: block;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #e2e8f0;
    background-color: white;
  }

  .event-list__items {
    background: transparent;
    border: none;
    max-height: none;
  }

  .event-list__content {
    background: transparent;
    border: none;
  }

  .event-list__item-contact {
    margin-bottom: 0.75rem;
  }

  .event-list__contact-name {
    font-size: 1rem;
    font-weight: 600;
  }

  .event-list__item-date {
    margin-bottom: 0.5rem;
  }

  .event-list__item-status {
    margin-top: 0.5rem;
    display: inline-block;
  }
}
