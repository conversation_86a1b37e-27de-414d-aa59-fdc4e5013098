'use strict';

var UI = require('./UI.js');
var getValueOrTextContent = require('./getValueOrTextContent.js');
var copySelection = require('./copySelection.js');
var trackValue = require('./trackValue.js');



exports.clearInitialValue = UI.clearInitialValue;
exports.getUISelection = UI.getUISelection;
exports.getUIValue = UI.getUIValue;
exports.setUISelection = UI.setUISelection;
exports.setUIValue = UI.setUIValue;
exports.getValueOrTextContent = getValueOrTextContent.getValueOrTextContent;
exports.copySelection = copySelection.copySelection;
exports.commitValueAfterInput = trackValue.commitValueAfterInput;
