import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from './App';

// Mock the API service
jest.mock('./services/api', () => ({
  importContacts: jest.fn(),
  scheduleReminders: jest.fn(),
  getEvents: jest.fn().mockResolvedValue([]),
  getChecklists: jest.fn().mockResolvedValue([]),
}));

describe('App Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders main navigation', () => {
    render(<App />);

    expect(screen.getByText('Reminder & Gifting')).toBeInTheDocument();
    expect(screen.getByText('Never miss an important date')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /events/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /add contact/i })).toBeInTheDocument();
  });

  test('shows events view by default', async () => {
    render(<App />);

    await waitFor(() => {
      expect(screen.getByText('Upcoming Events')).toBeInTheDocument();
      expect(screen.getByText('Manage your reminders and gift planning')).toBeInTheDocument();
    });
  });

  test('navigates to import view when add contact button is clicked', async () => {
    const user = userEvent.setup();
    render(<App />);

    const addContactButton = screen.getByRole('button', { name: /add contact/i });
    await user.click(addContactButton);

    expect(screen.getByText('Add New Contact')).toBeInTheDocument();
    expect(screen.getByText('Enter contact details to set up a new reminder')).toBeInTheDocument();
  });

  test('navigates back to events view when events button is clicked', async () => {
    const user = userEvent.setup();
    render(<App />);

    // Go to import view first
    const addContactButton = screen.getByRole('button', { name: /add contact/i });
    await user.click(addContactButton);

    // Then go back to events view
    const eventsButton = screen.getByRole('button', { name: /events/i });
    await user.click(eventsButton);

    await waitFor(() => {
      expect(screen.getByText('Upcoming Events')).toBeInTheDocument();
    });
  });

  test('shows active navigation state correctly', async () => {
    const user = userEvent.setup();
    render(<App />);

    // Events button should be active by default
    const eventsButton = screen.getByRole('button', { name: /events/i });
    expect(eventsButton).toHaveAttribute('aria-current', 'page');

    // Click add contact button
    const addContactButton = screen.getByRole('button', { name: /add contact/i });
    await user.click(addContactButton);

    // Add contact button should now be active
    expect(addContactButton).toHaveAttribute('aria-current', 'page');
    expect(eventsButton).not.toHaveAttribute('aria-current', 'page');
  });
});
