/**
 * Performance Tests
 * Tests for app performance, memory usage, and optimization
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../App';
import { EventList } from '../components/EventList';
import { ReminderCard } from '../components/ReminderCard';
import { Checklist } from '../components/Checklist';
import { IContact, IChecklistItem } from '../types/api';
import * as apiService from '../services/api';

// Mock the API service
jest.mock('../services/api', () => ({
  importContacts: jest.fn(),
  scheduleReminders: jest.fn(),
  getEvents: jest.fn(),
  getChecklists: jest.fn(),
}));

const mockGetEvents = apiService.getEvents as jest.MockedFunction<typeof apiService.getEvents>;
const mockGetChecklists = apiService.getChecklists as jest.MockedFunction<typeof apiService.getChecklists>;

// Generate large datasets for performance testing
const generateMockEvents = (count: number): IContact[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: `event-${index}`,
    contact_name: `Contact ${index}`,
    contact_phone: `+155512345${index.toString().padStart(2, '0')}`,
    event_date: new Date(2025, 11, 25 + (index % 30)).toISOString().split('T')[0],
    lead_time_days: 7 + (index % 14),
    reminder_sent: index % 3 === 0,
  }));
};

const generateMockChecklistItems = (count: number, eventId: string): IChecklistItem[] => {
  return Array.from({ length: count }, (_, index) => ({
    list_id: `item-${index}`,
    event_id: eventId,
    item: `Checklist item ${index} - ${Math.random().toString(36).substring(7)}`,
    status: index % 3 === 0 ? 'done' : 'pending',
  }));
};

describe('Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // ============================================================================
  // RENDERING PERFORMANCE TESTS
  // ============================================================================

  describe('Rendering Performance', () => {
    it('renders large event list efficiently', async () => {
      const largeEventList = generateMockEvents(100);
      mockGetEvents.mockResolvedValue(largeEventList);

      const startTime = performance.now();
      
      render(<EventList />);
      
      await waitFor(() => {
        expect(screen.getByText('Contact 0')).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render 100 events in less than 1 second
      expect(renderTime).toBeLessThan(1000);
      
      // Verify all events are rendered
      expect(screen.getByText('Contact 0')).toBeInTheDocument();
      expect(screen.getByText('Contact 99')).toBeInTheDocument();
    });

    it('handles large checklist efficiently', async () => {
      const largeChecklist = generateMockChecklistItems(50, 'event-1');
      mockGetChecklists.mockResolvedValue(largeChecklist);

      const startTime = performance.now();
      
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText(/checklist item 0/i)).toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render 50 checklist items in less than 500ms
      expect(renderTime).toBeLessThan(500);
    });

    it('maintains performance with rapid state updates', async () => {
      const user = userEvent.setup();
      const mockEvent: IContact = {
        id: '1',
        contact_name: 'John Doe',
        contact_phone: '+1234567890',
        event_date: '2025-12-25',
        lead_time_days: 7,
        reminder_sent: false,
      };

      const startTime = performance.now();
      
      render(
        <ReminderCard 
          event={mockEvent}
          onReminderSent={jest.fn()}
          onSnooze={jest.fn()}
          onEdit={jest.fn()}
          onDelete={jest.fn()}
        />
      );

      // Perform rapid interactions
      const expandButton = screen.getByRole('button', { name: /expand/i });
      
      // Rapidly toggle expand/collapse
      for (let i = 0; i < 10; i++) {
        await user.click(expandButton);
      }
      
      const endTime = performance.now();
      const interactionTime = endTime - startTime;
      
      // Should handle 10 rapid interactions in less than 200ms
      expect(interactionTime).toBeLessThan(200);
    });
  });

  // ============================================================================
  // MEMORY USAGE TESTS
  // ============================================================================

  describe('Memory Usage', () => {
    it('does not create memory leaks with component mounting/unmounting', async () => {
      const user = userEvent.setup();
      const largeEventList = generateMockEvents(50);
      mockGetEvents.mockResolvedValue(largeEventList);

      const { unmount } = render(<App />);

      // Wait for initial render
      await waitFor(() => {
        expect(screen.getByText('Contact 0')).toBeInTheDocument();
      });

      // Navigate between views multiple times
      for (let i = 0; i < 5; i++) {
        const addContactButton = screen.getByRole('button', { name: /add contact/i });
        await user.click(addContactButton);
        
        const eventsButton = screen.getAllByRole('button', { name: /events/i })[0];
        await user.click(eventsButton);
        
        await waitFor(() => {
          expect(screen.getByText('Contact 0')).toBeInTheDocument();
        });
      }

      // Unmount component
      unmount();

      // If we reach here without memory issues, the test passes
      expect(true).toBe(true);
    });

    it('efficiently handles large datasets without excessive re-renders', async () => {
      const user = userEvent.setup();
      const largeChecklist = generateMockChecklistItems(30, 'event-1');
      mockGetChecklists.mockResolvedValue(largeChecklist);

      let renderCount = 0;
      const TestWrapper = () => {
        renderCount++;
        return <Checklist eventId="event-1" />;
      };

      render(<TestWrapper />);

      await waitFor(() => {
        expect(screen.getByText(/checklist item 0/i)).toBeInTheDocument();
      });

      const initialRenderCount = renderCount;

      // Perform filter operation
      const filterSelect = screen.getByLabelText('Filter checklist items');
      await user.selectOptions(filterSelect, 'pending');

      await waitFor(() => {
        // Should not cause excessive re-renders
        expect(renderCount - initialRenderCount).toBeLessThan(3);
      });
    });
  });

  // ============================================================================
  // OPTIMIZATION TESTS
  // ============================================================================

  describe('Optimization Tests', () => {
    it('uses efficient search and filtering', async () => {
      const user = userEvent.setup();
      const largeEventList = generateMockEvents(100);
      mockGetEvents.mockResolvedValue(largeEventList);

      render(<EventList />);

      await waitFor(() => {
        expect(screen.getByText('Contact 0')).toBeInTheDocument();
      });

      const searchInput = screen.getByLabelText(/search events/i);
      
      const startTime = performance.now();
      
      // Type search query
      await user.type(searchInput, 'Contact 5');
      
      // Wait for search results
      await waitFor(() => {
        expect(screen.getByText('Contact 5')).toBeInTheDocument();
        expect(screen.queryByText('Contact 0')).not.toBeInTheDocument();
      });
      
      const endTime = performance.now();
      const searchTime = endTime - startTime;
      
      // Search should complete quickly even with large dataset
      expect(searchTime).toBeLessThan(300);
    });

    it('handles rapid user interactions without blocking UI', async () => {
      const user = userEvent.setup();
      const mockChecklist = generateMockChecklistItems(20, 'event-1');
      mockGetChecklists.mockResolvedValue(mockChecklist);

      render(<Checklist eventId="event-1" />);

      await waitFor(() => {
        expect(screen.getByText(/checklist item 0/i)).toBeInTheDocument();
      });

      const checkboxes = screen.getAllByRole('checkbox');
      
      const startTime = performance.now();
      
      // Rapidly toggle multiple checkboxes
      for (let i = 0; i < Math.min(10, checkboxes.length); i++) {
        await user.click(checkboxes[i]);
      }
      
      const endTime = performance.now();
      const interactionTime = endTime - startTime;
      
      // Should handle rapid interactions smoothly
      expect(interactionTime).toBeLessThan(500);
    });

    it('efficiently updates component state without unnecessary re-renders', async () => {
      const user = userEvent.setup();
      let renderCount = 0;
      
      const mockEvent: IContact = {
        id: '1',
        contact_name: 'John Doe',
        contact_phone: '+1234567890',
        event_date: '2025-12-25',
        lead_time_days: 7,
        reminder_sent: false,
      };

      const TestWrapper = () => {
        renderCount++;
        return (
          <ReminderCard 
            event={mockEvent}
            onReminderSent={jest.fn()}
            onSnooze={jest.fn()}
            onEdit={jest.fn()}
            onDelete={jest.fn()}
          />
        );
      };

      render(<TestWrapper />);

      const initialRenderCount = renderCount;

      // Expand details
      const expandButton = screen.getByRole('button', { name: /expand/i });
      await user.click(expandButton);

      // Open snooze dropdown
      const snoozeButton = screen.getByRole('button', { name: /snooze/i });
      await user.click(snoozeButton);

      // Close snooze dropdown
      const closeButton = screen.getByRole('button', { name: /close snooze options/i });
      await user.click(closeButton);

      // Should not cause excessive re-renders
      expect(renderCount - initialRenderCount).toBeLessThan(5);
    });
  });

  // ============================================================================
  // STRESS TESTS
  // ============================================================================

  describe('Stress Tests', () => {
    it('handles maximum realistic data load', async () => {
      // Test with maximum expected data
      const maxEvents = generateMockEvents(500);
      const maxChecklist = generateMockChecklistItems(100, 'event-1');
      
      mockGetEvents.mockResolvedValue(maxEvents);
      mockGetChecklists.mockResolvedValue(maxChecklist);

      const startTime = performance.now();
      
      render(<App />);
      
      await waitFor(() => {
        expect(screen.getByText('Contact 0')).toBeInTheDocument();
      }, { timeout: 5000 });
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      // Should handle maximum load in reasonable time
      expect(loadTime).toBeLessThan(3000);
    });

    it('maintains responsiveness under heavy interaction load', async () => {
      const user = userEvent.setup();
      const largeEventList = generateMockEvents(200);
      mockGetEvents.mockResolvedValue(largeEventList);

      render(<App />);

      await waitFor(() => {
        expect(screen.getByText('Contact 0')).toBeInTheDocument();
      });

      const startTime = performance.now();
      
      // Perform many rapid operations
      const searchInput = screen.getByLabelText(/search events/i);
      const filterSelect = screen.getByLabelText(/filter by status/i);
      
      // Rapid search operations
      await user.type(searchInput, 'Contact');
      await user.clear(searchInput);
      await user.type(searchInput, '1');
      
      // Rapid filter changes
      await user.selectOptions(filterSelect, 'pending');
      await user.selectOptions(filterSelect, 'sent');
      await user.selectOptions(filterSelect, 'all');
      
      const endTime = performance.now();
      const operationTime = endTime - startTime;
      
      // Should remain responsive under heavy load
      expect(operationTime).toBeLessThan(1000);
    });
  });
});
