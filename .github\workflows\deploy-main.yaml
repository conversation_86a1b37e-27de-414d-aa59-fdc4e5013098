name: Deploy to Production

on:
  push:
    branches: [main]
  release:
    types: [published]

jobs:
  deploy-production:
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci
        
      - name: Run full test suite
        working-directory: ./frontend
        run: npm test -- --coverage --watchAll=false
        
      - name: Build for production
        working-directory: ./frontend
        run: npm run build
        env:
          REACT_APP_API_URL: ${{ secrets.PRODUCTION_API_URL }}
          REACT_APP_ENVIRONMENT: production
          
      - name: Deploy to Vercel (Production)
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./frontend
          scope: ${{ secrets.VERCEL_ORG_ID }}
          
      - name: Create deployment tag
        if: github.event_name == 'push'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git tag -a "v$(date +'%Y%m%d-%H%M%S')" -m "Production deployment $(date)"
          git push origin --tags
          
      - name: Notify production deployment
        run: echo "🎉 Production deployment completed successfully!"

  post-deployment-checks:
    needs: deploy-production
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Health check
        run: |
          # Wait for deployment to be ready
          sleep 30
          
          # Basic health check (replace with actual production URL)
          if curl -f -s "${{ secrets.PRODUCTION_URL }}" > /dev/null; then
            echo "✅ Production site is responding"
          else
            echo "❌ Production site health check failed"
            exit 1
          fi
          
      - name: Performance audit
        run: |
          # Placeholder for performance testing
          echo "🔍 Performance audit would run here"
          # Could integrate with Lighthouse CI or similar tools
