/**
 * App Integration Tests
 * End-to-end component interaction testing for complete user workflows
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../App';
import { IContact, IChecklistItem } from '../types/api';
import * as apiService from '../services/api';

// Mock the API service
jest.mock('../services/api', () => ({
  importContacts: jest.fn(),
  scheduleReminders: jest.fn(),
  getEvents: jest.fn(),
  getChecklists: jest.fn(),
}));

const mockImportContacts = apiService.importContacts as jest.MockedFunction<typeof apiService.importContacts>;
const mockScheduleReminders = apiService.scheduleReminders as jest.MockedFunction<typeof apiService.scheduleReminders>;
const mockGetEvents = apiService.getEvents as jest.MockedFunction<typeof apiService.getEvents>;
const mockGetChecklists = apiService.getChecklists as jest.MockedFunction<typeof apiService.getChecklists>;

// Sample test data
const mockEvents: IContact[] = [
  {
    id: '1',
    contact_name: 'John Doe',
    contact_phone: '+1234567890',
    event_date: '2025-12-25',
    lead_time_days: 7,
    reminder_sent: false,
  },
  {
    id: '2',
    contact_name: 'Jane Smith',
    contact_phone: '+0987654321',
    event_date: '2025-11-15',
    lead_time_days: 14,
    reminder_sent: true,
  },
];

const mockChecklistItems: IChecklistItem[] = [
  {
    list_id: '1',
    event_id: '1',
    item: 'Buy Christmas gift',
    status: 'pending',
  },
  {
    list_id: '2',
    event_id: '1',
    item: 'Wrap the gift',
    status: 'done',
  },
];

describe('App Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetEvents.mockResolvedValue(mockEvents);
    mockGetChecklists.mockResolvedValue(mockChecklistItems);
    mockImportContacts.mockResolvedValue(undefined);
    mockScheduleReminders.mockResolvedValue(undefined);
  });

  // ============================================================================
  // COMPLETE USER WORKFLOW TESTS
  // ============================================================================

  describe('Complete User Workflows', () => {
    it('allows user to import contact, view events, and manage reminders', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Step 1: Navigate to import form
      const addContactButton = screen.getByRole('button', { name: /add contact/i });
      await user.click(addContactButton);

      expect(screen.getByText('Add New Contact')).toBeInTheDocument();

      // Step 2: Fill out import form
      const nameInput = screen.getByLabelText(/contact name/i);
      const phoneInput = screen.getByLabelText(/phone number/i);
      const dateInput = screen.getByLabelText(/event date/i);
      const leadTimeInput = screen.getByLabelText(/lead time/i);

      await user.type(nameInput, 'Test Contact');
      await user.type(phoneInput, '+1555123456');
      await user.type(dateInput, '2025-12-31');
      await user.type(leadTimeInput, '10');

      // Step 3: Submit form
      const submitButton = screen.getByRole('button', { name: /import contact/i });
      await user.click(submitButton);

      // Verify API was called
      await waitFor(() => {
        expect(mockImportContacts).toHaveBeenCalledWith([{
          contact_name: 'Test Contact',
          contact_phone: '+1555123456',
          event_date: '2025-12-31',
          lead_time_days: 10,
        }]);
      });

      // Step 4: Navigate back to events list
      await waitFor(() => {
        const eventsButton = screen.getAllByRole('button', { name: /events/i })[0];
        return user.click(eventsButton);
      });

      // Step 5: Verify events are displayed
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
      });
    });

    it('allows user to select event, view details, and manage checklist', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Wait for events to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Step 1: Select an event
      const eventCard = screen.getByText('John Doe').closest('.event-list__item');
      expect(eventCard).toBeInTheDocument();
      
      if (eventCard) {
        await user.click(eventCard);
      }

      // Step 2: Verify detail view is shown
      await waitFor(() => {
        expect(screen.getByText('Event Details')).toBeInTheDocument();
        expect(screen.getByText(/manage reminder and gift planning for john doe/i)).toBeInTheDocument();
      });

      // Step 3: Open checklist
      const checklistButton = screen.getByRole('button', { name: /view gift planning checklist/i });
      await user.click(checklistButton);

      // Step 4: Verify checklist is displayed
      await waitFor(() => {
        expect(screen.getByText('Gift Planning Checklist')).toBeInTheDocument();
        expect(screen.getByText('Buy Christmas gift')).toBeInTheDocument();
        expect(screen.getByText('Wrap the gift')).toBeInTheDocument();
      });

      // Step 5: Add new checklist item
      const addItemButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addItemButton);

      const itemInput = screen.getByPlaceholderText('Enter gift planning item...');
      await user.type(itemInput, 'Write Christmas card');

      const submitItemButton = screen.getByRole('button', { name: 'Add Item' });
      await user.click(submitItemButton);

      // Step 6: Verify new item appears
      await waitFor(() => {
        expect(screen.getByText('Write Christmas card')).toBeInTheDocument();
      });
    });

    it('handles reminder management workflow', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Wait for events to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Step 1: Select an event
      const eventCard = screen.getByText('John Doe').closest('.event-list__item');
      if (eventCard) {
        await user.click(eventCard);
      }

      // Step 2: Verify reminder card is shown
      await waitFor(() => {
        expect(screen.getByText('Event Details')).toBeInTheDocument();
      });

      // Step 3: Mark reminder as sent
      const markSentButton = screen.getByRole('button', { name: /mark reminder as sent/i });
      await user.click(markSentButton);

      // Verify API was called
      await waitFor(() => {
        expect(mockScheduleReminders).toHaveBeenCalled();
      });

      // Step 4: Test snooze functionality
      const snoozeButton = screen.getByRole('button', { name: /snooze reminder/i });
      await user.click(snoozeButton);

      // Verify snooze dropdown appears
      expect(screen.getByText('Snooze for:')).toBeInTheDocument();
      expect(screen.getByText('1 Hour')).toBeInTheDocument();
      expect(screen.getByText('1 Day')).toBeInTheDocument();

      // Select snooze option
      const oneDayOption = screen.getByText('1 Day');
      await user.click(oneDayOption);

      // Verify snooze dropdown closes
      await waitFor(() => {
        expect(screen.queryByText('Snooze for:')).not.toBeInTheDocument();
      });
    });
  });

  // ============================================================================
  // NAVIGATION AND STATE MANAGEMENT TESTS
  // ============================================================================

  describe('Navigation and State Management', () => {
    it('maintains state correctly across navigation', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Step 1: Select an event
      const eventCard = screen.getByText('John Doe').closest('.event-list__item');
      if (eventCard) {
        await user.click(eventCard);
      }

      // Step 2: Verify detail view
      await waitFor(() => {
        expect(screen.getByText('Event Details')).toBeInTheDocument();
      });

      // Step 3: Navigate to import form
      const addContactButton = screen.getByRole('button', { name: /add contact/i });
      await user.click(addContactButton);

      expect(screen.getByText('Add New Contact')).toBeInTheDocument();

      // Step 4: Navigate back to events
      const eventsButton = screen.getAllByRole('button', { name: /events/i })[0];
      await user.click(eventsButton);

      // Step 5: Verify we're back to list view (not detail view)
      await waitFor(() => {
        expect(screen.getByText('Upcoming Events')).toBeInTheDocument();
        expect(screen.queryByText('Event Details')).not.toBeInTheDocument();
      });

      // Step 6: Re-select the same event
      const eventCardAgain = screen.getByText('John Doe').closest('.event-list__item');
      if (eventCardAgain) {
        await user.click(eventCardAgain);
      }

      // Step 7: Verify detail view is restored
      await waitFor(() => {
        expect(screen.getByText('Event Details')).toBeInTheDocument();
      });
    });

    it('handles active navigation states correctly', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Initial state - Events should be active
      const eventsButton = screen.getAllByRole('button', { name: /events/i })[0];
      const addContactButton = screen.getByRole('button', { name: /add contact/i });

      expect(eventsButton).toHaveAttribute('aria-current', 'page');
      expect(addContactButton).not.toHaveAttribute('aria-current', 'page');

      // Navigate to import form
      await user.click(addContactButton);

      expect(addContactButton).toHaveAttribute('aria-current', 'page');
      expect(eventsButton).not.toHaveAttribute('aria-current', 'page');

      // Navigate back to events
      await user.click(eventsButton);

      expect(eventsButton).toHaveAttribute('aria-current', 'page');
      expect(addContactButton).not.toHaveAttribute('aria-current', 'page');
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling', () => {
    it('handles API errors gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock API failure
      mockImportContacts.mockRejectedValue(new Error('Network error'));
      
      render(<App />);

      // Navigate to import form
      const addContactButton = screen.getByRole('button', { name: /add contact/i });
      await user.click(addContactButton);

      // Fill out form
      const nameInput = screen.getByLabelText(/contact name/i);
      await user.type(nameInput, 'Test Contact');

      const phoneInput = screen.getByLabelText(/phone number/i);
      await user.type(phoneInput, '+1555123456');

      const dateInput = screen.getByLabelText(/event date/i);
      await user.type(dateInput, '2025-12-31');

      const leadTimeInput = screen.getByLabelText(/lead time/i);
      await user.type(leadTimeInput, '10');

      // Submit form
      const submitButton = screen.getByRole('button', { name: /import contact/i });
      await user.click(submitButton);

      // Verify error is displayed
      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });

      // Verify error can be dismissed
      const dismissButton = screen.getByRole('button', { name: /dismiss error/i });
      await user.click(dismissButton);

      await waitFor(() => {
        expect(screen.queryByText('Network error')).not.toBeInTheDocument();
      });
    });

    it('handles component-level errors without breaking the app', async () => {
      const user = userEvent.setup();
      
      // Mock events API failure
      mockGetEvents.mockRejectedValue(new Error('Failed to load events'));
      
      render(<App />);

      // Wait for error to appear
      await waitFor(() => {
        expect(screen.getByText('Failed to load events')).toBeInTheDocument();
      });

      // Verify navigation still works
      const addContactButton = screen.getByRole('button', { name: /add contact/i });
      await user.click(addContactButton);

      expect(screen.getByText('Add New Contact')).toBeInTheDocument();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility', () => {
    it('supports keyboard navigation throughout the app', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Tab through navigation
      await user.tab();
      expect(screen.getAllByRole('button', { name: /events/i })[0]).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /add contact/i })).toHaveFocus();

      // Navigate to import form with keyboard
      await user.keyboard('{Enter}');
      expect(screen.getByText('Add New Contact')).toBeInTheDocument();

      // Tab through form fields
      await user.tab();
      expect(screen.getByLabelText(/contact name/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/phone number/i)).toHaveFocus();
    });

    it('provides proper ARIA labels and roles', async () => {
      render(<App />);

      // Check main navigation
      const navigation = screen.getByRole('navigation');
      expect(navigation).toHaveAttribute('aria-label', 'Main navigation');

      // Check main content area
      const main = screen.getByRole('main');
      expect(main).toBeInTheDocument();

      // Check application role
      const app = screen.getByRole('application');
      expect(app).toBeInTheDocument();

      // Wait for events to load and check event list
      await waitFor(() => {
        const eventsList = screen.getByRole('region', { name: /events list/i });
        expect(eventsList).toBeInTheDocument();
      });
    });
  });
});
