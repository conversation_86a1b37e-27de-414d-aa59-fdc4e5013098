/**
 * TypeScript interfaces for API data structures
 * Based on Apps Script backend implementation in apps-script/Code.gs
 */

// ============================================================================
// REQUEST INTERFACES
// ============================================================================

export interface IContactData {
  contact_name: string;
  contact_phone: string;
  event_date: string; // YYYY-MM-DD format
  lead_time_days: number;
}

export interface IImportContactsRequest {
  data: IContactData[];
}

export interface IGetChecklistsRequest {
  eventId: string;
}

// ============================================================================
// RESPONSE INTERFACES
// ============================================================================

export interface IContact {
  id: string;
  contact_name: string;
  contact_phone: string;
  event_date: string; // YYYY-MM-DD format
  lead_time_days: number;
  reminder_sent: boolean;
}

export interface IChecklistItem {
  list_id: string;
  event_id: string;
  item: string;
  status: 'pending' | 'done';
}

export interface IImportContactsResponse {
  success: true;
  importedCount: number;
}

export interface IGetEventsResponse extends Array<IContact> {}

export interface IScheduleRemindersResponse {
  scheduledCount: number;
}

export interface IGetChecklistsResponse extends Array<IChecklistItem> {}

// ============================================================================
// ERROR INTERFACES
// ============================================================================

export interface IApiError {
  code: string;
  message: string;
}

export interface IImportContactsError {
  error: {
    code: 'IMPORT_ERROR';
    message: string;
  };
}

export interface IGetEventsError {
  error: {
    code: 'FETCH_ERROR';
    message: string;
  };
}

export interface IScheduleRemindersError {
  error: {
    code: 'SCHEDULE_ERROR';
    message: string;
  };
}

export interface IGetChecklistsError {
  error: {
    code: 'CHECKLIST_ERROR';
    message: string;
  };
}

export interface IServerError {
  error: {
    code: 'SERVER_ERROR';
    message: string;
  };
}

export interface IInvalidActionError {
  error: {
    code: 'INVALID_ACTION';
    message: string;
  };
}

// ============================================================================
// UNION TYPES FOR API RESPONSES
// ============================================================================

export type ImportContactsResult =
  | IImportContactsResponse
  | IImportContactsError
  | IServerError;
export type GetEventsResult =
  | IGetEventsResponse
  | IGetEventsError
  | IServerError;
export type ScheduleRemindersResult =
  | IScheduleRemindersResponse
  | IScheduleRemindersError
  | IServerError;
export type GetChecklistsResult =
  | IGetChecklistsResponse
  | IGetChecklistsError
  | IServerError;

// ============================================================================
// UTILITY TYPE GUARDS
// ============================================================================

export function isApiError(response: any): response is { error: IApiError } {
  return response && typeof response === 'object' && 'error' in response;
}

export function isImportContactsSuccess(
  response: ImportContactsResult
): response is IImportContactsResponse {
  return (
    !isApiError(response) &&
    'success' in response &&
    'importedCount' in response
  );
}

export function isGetEventsSuccess(
  response: GetEventsResult
): response is IGetEventsResponse {
  return !isApiError(response) && Array.isArray(response);
}

export function isScheduleRemindersSuccess(
  response: ScheduleRemindersResult
): response is IScheduleRemindersResponse {
  return !isApiError(response) && 'scheduledCount' in response;
}

export function isGetChecklistsSuccess(
  response: GetChecklistsResult
): response is IGetChecklistsResponse {
  return !isApiError(response) && Array.isArray(response);
}

// ============================================================================
// API CONFIGURATION
// ============================================================================

export interface IApiConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

export const DEFAULT_API_CONFIG: IApiConfig = {
  baseUrl: process.env.REACT_APP_API_URL || '',
  timeout: 10000, // 10 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
};
