/**
 * Unit tests for ImportForm component
 * Tests form validation, submission, error handling, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ImportForm } from './index';
import { IContactData } from '../../types/api';

describe('ImportForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
    mockOnSuccess.mockClear();
    mockOnError.mockClear();
  });

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onSuccess: mockOnSuccess,
    onError: mockOnError,
  };

  describe('Rendering', () => {
    it('should render all form fields', () => {
      render(<ImportForm {...defaultProps} />);

      expect(screen.getByLabelText(/contact name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/event date/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/lead time/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /import contact/i })).toBeInTheDocument();
    });

    it('should render with initial data', () => {
      const initialData = {
        contact_name: 'John Doe',
        contact_phone: '+1234567890',
        event_date: '2025-12-25',
        lead_time_days: 14,
      };

      render(<ImportForm {...defaultProps} initialData={initialData} />);

      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+1234567890')).toBeInTheDocument();
      expect(screen.getByDisplayValue('2025-12-25')).toBeInTheDocument();
      expect(screen.getByDisplayValue('14')).toBeInTheDocument();
    });

    it('should show loading state when isLoading is true', () => {
      render(<ImportForm {...defaultProps} isLoading={true} />);

      const submitButton = screen.getByRole('button', { name: /importing/i });
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Form Validation', () => {
    it('should show error for empty contact name', async () => {
      const user = userEvent.setup();
      render(<ImportForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/contact name/i);
      const submitButton = screen.getByRole('button', { name: /import contact/i });

      await user.click(nameInput);
      await user.tab(); // Trigger blur event
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/contact name is required/i)).toBeInTheDocument();
      });
    });

    it('should show error for invalid phone number', async () => {
      const user = userEvent.setup();
      render(<ImportForm {...defaultProps} />);

      const phoneInput = screen.getByLabelText(/phone number/i);
      await user.type(phoneInput, '123');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument();
      });
    });

    it('should show error for past event date', async () => {
      const user = userEvent.setup();
      render(<ImportForm {...defaultProps} />);

      const dateInput = screen.getByLabelText(/event date/i);
      const pastDate = '2020-01-01';
      
      await user.type(dateInput, pastDate);
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText(/event date cannot be in the past/i)).toBeInTheDocument();
      });
    });

    it('should show error for invalid lead time', async () => {
      const user = userEvent.setup();
      render(<ImportForm {...defaultProps} />);

      const leadTimeInput = screen.getByLabelText(/lead time/i);
      await user.clear(leadTimeInput);
      await user.type(leadTimeInput, '0');
      await user.tab();

      await waitFor(() => {
        expect(screen.getByText(/lead time must be at least 1 day/i)).toBeInTheDocument();
      });
    });

    it('should validate all fields before submission', async () => {
      const user = userEvent.setup();
      render(<ImportForm {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: /import contact/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/contact name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/phone number is required/i)).toBeInTheDocument();
        expect(screen.getByText(/event date is required/i)).toBeInTheDocument();
      });

      expect(mockOnSubmit).not.toHaveBeenCalled();
    });
  });

  describe('Form Submission', () => {
    const validFormData = {
      contact_name: 'John Doe',
      contact_phone: '+1234567890',
      event_date: '2025-12-25',
      lead_time_days: 7,
    };

    it('should submit valid form data', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValueOnce(undefined);

      render(<ImportForm {...defaultProps} />);

      // Fill out form
      await user.type(screen.getByLabelText(/contact name/i), validFormData.contact_name);
      await user.type(screen.getByLabelText(/phone number/i), validFormData.contact_phone);
      await user.type(screen.getByLabelText(/event date/i), validFormData.event_date);
      await user.clear(screen.getByLabelText(/lead time/i));
      await user.type(screen.getByLabelText(/lead time/i), validFormData.lead_time_days.toString());

      // Submit form
      await user.click(screen.getByRole('button', { name: /import contact/i }));

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(validFormData);
      });
    });

    it('should handle submission success', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValueOnce(undefined);

      render(<ImportForm {...defaultProps} showSuccessMessage={true} />);

      // Fill and submit form
      await user.type(screen.getByLabelText(/contact name/i), validFormData.contact_name);
      await user.type(screen.getByLabelText(/phone number/i), validFormData.contact_phone);
      await user.type(screen.getByLabelText(/event date/i), validFormData.event_date);
      await user.click(screen.getByRole('button', { name: /import contact/i }));

      await waitFor(() => {
        expect(screen.getByText(/contact imported successfully/i)).toBeInTheDocument();
      });
    });

    it('should handle submission error', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Failed to import contact';
      mockOnSubmit.mockRejectedValueOnce(new Error(errorMessage));

      render(<ImportForm {...defaultProps} />);

      // Fill and submit form
      await user.type(screen.getByLabelText(/contact name/i), validFormData.contact_name);
      await user.type(screen.getByLabelText(/phone number/i), validFormData.contact_phone);
      await user.type(screen.getByLabelText(/event date/i), validFormData.event_date);
      await user.click(screen.getByRole('button', { name: /import contact/i }));

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
        expect(mockOnError).toHaveBeenCalledWith(errorMessage);
      });
    });

    it('should reset form after successful submission when resetOnSuccess is true', async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockResolvedValueOnce(undefined);

      render(<ImportForm {...defaultProps} resetOnSuccess={true} />);

      const nameInput = screen.getByLabelText(/contact name/i);
      
      // Fill and submit form
      await user.type(nameInput, validFormData.contact_name);
      await user.type(screen.getByLabelText(/phone number/i), validFormData.contact_phone);
      await user.type(screen.getByLabelText(/event date/i), validFormData.event_date);
      await user.click(screen.getByRole('button', { name: /import contact/i }));

      await waitFor(() => {
        expect(nameInput).toHaveValue('');
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<ImportForm {...defaultProps} />);

      expect(screen.getByLabelText(/contact name/i)).toHaveAttribute('aria-required', 'true');
      expect(screen.getByLabelText(/phone number/i)).toHaveAttribute('aria-required', 'true');
      expect(screen.getByLabelText(/event date/i)).toHaveAttribute('aria-required', 'true');
      expect(screen.getByLabelText(/lead time/i)).toHaveAttribute('aria-required', 'true');
    });

    it('should associate error messages with form fields', async () => {
      const user = userEvent.setup();
      render(<ImportForm {...defaultProps} />);

      const nameInput = screen.getByLabelText(/contact name/i);
      await user.click(nameInput);
      await user.tab();

      await waitFor(() => {
        const errorMessage = screen.getByText(/contact name is required/i);
        expect(nameInput).toHaveAttribute('aria-describedby', expect.stringContaining(errorMessage.id));
      });
    });
  });
});
