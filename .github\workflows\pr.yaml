name: Pull Request Checks

on:
  pull_request:
    branches: [develop, main]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci
        
      - name: Run Prettier check
        working-directory: ./frontend
        run: npx prettier --check "src/**/*.{js,jsx,ts,tsx,json,css,md}"
        
      - name: Run ESLint
        working-directory: ./frontend
        run: npx eslint "src/**/*.{js,jsx,ts,tsx}" --max-warnings 0
        
      - name: Run TypeScript check
        working-directory: ./frontend
        run: npx tsc --noEmit
        
      - name: Run tests
        working-directory: ./frontend
        run: npm test -- --coverage --watchAll=false
        
      - name: Build application
        working-directory: ./frontend
        run: npm run build
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          directory: ./frontend/coverage
          fail_ci_if_error: false

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
          
      - name: Install dependencies
        working-directory: ./frontend
        run: npm ci
        
      - name: Run security audit
        working-directory: ./frontend
        run: npm audit --audit-level moderate
        
      - name: Check for sensitive data
        run: |
          if grep -r "console\.log.*password\|console\.log.*token\|console\.log.*key" frontend/src/; then
            echo "Potential sensitive data found in console.log statements"
            exit 1
          fi
