/**
 * User Scenarios End-to-End Tests
 * Real-world user scenarios and edge cases
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../App';
import { IContact, IChecklistItem } from '../types/api';
import * as apiService from '../services/api';

// Mock the API service
jest.mock('../services/api', () => ({
  importContacts: jest.fn(),
  scheduleReminders: jest.fn(),
  getEvents: jest.fn(),
  getChecklists: jest.fn(),
}));

const mockImportContacts = apiService.importContacts as jest.MockedFunction<typeof apiService.importContacts>;
const mockScheduleReminders = apiService.scheduleReminders as jest.MockedFunction<typeof apiService.scheduleReminders>;
const mockGetEvents = apiService.getEvents as jest.MockedFunction<typeof apiService.getEvents>;
const mockGetChecklists = apiService.getChecklists as jest.MockedFunction<typeof apiService.getChecklists>;

describe('User Scenarios End-to-End Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetEvents.mockResolvedValue([]);
    mockGetChecklists.mockResolvedValue([]);
    mockImportContacts.mockResolvedValue(undefined);
    mockScheduleReminders.mockResolvedValue(undefined);
  });

  // ============================================================================
  // FIRST-TIME USER SCENARIOS
  // ============================================================================

  describe('First-Time User Experience', () => {
    it('guides new user through complete setup process', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Step 1: User sees empty state
      await waitFor(() => {
        expect(screen.getByText(/no events found/i)).toBeInTheDocument();
        expect(screen.getByText(/import some contacts to get started/i)).toBeInTheDocument();
      });

      // Step 2: User navigates to import form
      const addContactButton = screen.getByRole('button', { name: /add contact/i });
      await user.click(addContactButton);

      expect(screen.getByText('Add New Contact')).toBeInTheDocument();
      expect(screen.getByText('Enter contact details to set up a new reminder')).toBeInTheDocument();

      // Step 3: User fills out their first contact
      await user.type(screen.getByLabelText(/contact name/i), 'Mom');
      await user.type(screen.getByLabelText(/phone number/i), '+1234567890');
      await user.type(screen.getByLabelText(/event date/i), '2025-05-12'); // Mother's Day
      await user.type(screen.getByLabelText(/lead time/i), '14');

      // Step 4: User submits form
      const submitButton = screen.getByRole('button', { name: /import contact/i });
      await user.click(submitButton);

      // Step 5: Verify success and navigation back to events
      await waitFor(() => {
        expect(mockImportContacts).toHaveBeenCalledWith([{
          contact_name: 'Mom',
          contact_phone: '+1234567890',
          event_date: '2025-05-12',
          lead_time_days: 14,
        }]);
      });

      // Mock the updated events list
      const newEvent: IContact = {
        id: '1',
        contact_name: 'Mom',
        contact_phone: '+1234567890',
        event_date: '2025-05-12',
        lead_time_days: 14,
        reminder_sent: false,
      };
      mockGetEvents.mockResolvedValue([newEvent]);

      // Step 6: User sees their first event
      await waitFor(() => {
        expect(screen.getByText('Mom')).toBeInTheDocument();
      });
    });

    it('handles user making mistakes during setup', async () => {
      const user = userEvent.setup();
      render(<App />);

      // Navigate to import form
      const addContactButton = screen.getByRole('button', { name: /add contact/i });
      await user.click(addContactButton);

      // User tries to submit empty form
      const submitButton = screen.getByRole('button', { name: /import contact/i });
      await user.click(submitButton);

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/contact name is required/i)).toBeInTheDocument();
      });

      // User fills out form with invalid data
      await user.type(screen.getByLabelText(/contact name/i), 'A'); // Too short
      await user.type(screen.getByLabelText(/phone number/i), '123'); // Invalid format
      await user.type(screen.getByLabelText(/event date/i), '2020-01-01'); // Past date
      await user.type(screen.getByLabelText(/lead time/i), '0'); // Invalid lead time

      await user.click(submitButton);

      // Should show multiple validation errors
      await waitFor(() => {
        expect(screen.getByText(/contact name must be at least 2 characters/i)).toBeInTheDocument();
        expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument();
        expect(screen.getByText(/event date cannot be in the past/i)).toBeInTheDocument();
        expect(screen.getByText(/lead time must be at least 1 day/i)).toBeInTheDocument();
      });

      // User corrects the errors
      await user.clear(screen.getByLabelText(/contact name/i));
      await user.type(screen.getByLabelText(/contact name/i), 'John Doe');
      
      await user.clear(screen.getByLabelText(/phone number/i));
      await user.type(screen.getByLabelText(/phone number/i), '+1234567890');
      
      await user.clear(screen.getByLabelText(/event date/i));
      await user.type(screen.getByLabelText(/event date/i), '2025-12-25');
      
      await user.clear(screen.getByLabelText(/lead time/i));
      await user.type(screen.getByLabelText(/lead time/i), '7');

      await user.click(submitButton);

      // Should succeed this time
      await waitFor(() => {
        expect(mockImportContacts).toHaveBeenCalledWith([{
          contact_name: 'John Doe',
          contact_phone: '+1234567890',
          event_date: '2025-12-25',
          lead_time_days: 7,
        }]);
      });
    });
  });

  // ============================================================================
  // POWER USER SCENARIOS
  // ============================================================================

  describe('Power User Workflows', () => {
    it('handles user managing multiple events efficiently', async () => {
      const user = userEvent.setup();
      
      // Setup multiple events
      const multipleEvents: IContact[] = [
        {
          id: '1',
          contact_name: 'Mom',
          contact_phone: '+1111111111',
          event_date: '2025-05-12',
          lead_time_days: 14,
          reminder_sent: false,
        },
        {
          id: '2',
          contact_name: 'Dad',
          contact_phone: '+2222222222',
          event_date: '2025-06-15',
          lead_time_days: 7,
          reminder_sent: true,
        },
        {
          id: '3',
          contact_name: 'Sister',
          contact_phone: '+3333333333',
          event_date: '2025-08-20',
          lead_time_days: 10,
          reminder_sent: false,
        },
      ];
      
      mockGetEvents.mockResolvedValue(multipleEvents);
      
      render(<App />);

      // Wait for events to load
      await waitFor(() => {
        expect(screen.getByText('Mom')).toBeInTheDocument();
        expect(screen.getByText('Dad')).toBeInTheDocument();
        expect(screen.getByText('Sister')).toBeInTheDocument();
      });

      // User filters to see only pending reminders
      const filterSelect = screen.getByLabelText(/filter by status/i);
      await user.selectOptions(filterSelect, 'pending');

      // Should only show Mom and Sister (pending reminders)
      expect(screen.getByText('Mom')).toBeInTheDocument();
      expect(screen.getByText('Sister')).toBeInTheDocument();
      expect(screen.queryByText('Dad')).not.toBeInTheDocument();

      // User searches for specific contact
      const searchInput = screen.getByLabelText(/search events/i);
      await user.type(searchInput, 'Mom');

      // Should only show Mom
      expect(screen.getByText('Mom')).toBeInTheDocument();
      expect(screen.queryByText('Sister')).not.toBeInTheDocument();

      // User clears search and selects an event
      await user.clear(searchInput);
      await user.selectOptions(filterSelect, 'all');

      await waitFor(() => {
        expect(screen.getByText('Mom')).toBeInTheDocument();
        expect(screen.getByText('Dad')).toBeInTheDocument();
        expect(screen.getByText('Sister')).toBeInTheDocument();
      });

      // User selects Mom's event
      const momEvent = screen.getByText('Mom').closest('.event-list__item');
      if (momEvent) {
        await user.click(momEvent);
      }

      // Should navigate to detail view
      await waitFor(() => {
        expect(screen.getByText('Event Details')).toBeInTheDocument();
        expect(screen.getByText(/manage reminder and gift planning for mom/i)).toBeInTheDocument();
      });
    });

    it('handles complex checklist management workflow', async () => {
      const user = userEvent.setup();
      
      const event: IContact = {
        id: '1',
        contact_name: 'Mom',
        contact_phone: '+1234567890',
        event_date: '2025-05-12',
        lead_time_days: 14,
        reminder_sent: false,
      };

      const initialChecklist: IChecklistItem[] = [
        {
          list_id: '1',
          event_id: '1',
          item: 'Buy flowers',
          status: 'done',
        },
        {
          list_id: '2',
          event_id: '1',
          item: 'Make dinner reservation',
          status: 'pending',
        },
      ];

      mockGetEvents.mockResolvedValue([event]);
      mockGetChecklists.mockResolvedValue(initialChecklist);

      render(<App />);

      // Navigate to event details
      await waitFor(() => {
        expect(screen.getByText('Mom')).toBeInTheDocument();
      });

      const eventCard = screen.getByText('Mom').closest('.event-list__item');
      if (eventCard) {
        await user.click(eventCard);
      }

      // Open checklist
      await waitFor(() => {
        const checklistButton = screen.getByRole('button', { name: /view gift planning checklist/i });
        await user.click(checklistButton);
      });

      // Verify initial checklist
      await waitFor(() => {
        expect(screen.getByText('Buy flowers')).toBeInTheDocument();
        expect(screen.getByText('Make dinner reservation')).toBeInTheDocument();
        expect(screen.getByText('1 of 2 completed (50%)')).toBeInTheDocument();
      });

      // User adds new item
      const addButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addButton);

      const itemInput = screen.getByPlaceholderText('Enter gift planning item...');
      await user.type(itemInput, 'Buy gift card');
      await user.keyboard('{Enter}');

      // Verify new item appears
      await waitFor(() => {
        expect(screen.getByText('Buy gift card')).toBeInTheDocument();
      });

      // User completes pending item
      const checkboxes = screen.getAllByRole('checkbox');
      const pendingCheckbox = checkboxes.find(cb => 
        cb.closest('.checklist__item')?.textContent?.includes('Make dinner reservation')
      );
      
      if (pendingCheckbox) {
        await user.click(pendingCheckbox);
      }

      // User edits an item
      const editButtons = screen.getAllByLabelText(/Edit/);
      await user.click(editButtons[0]);

      const editInput = screen.getByDisplayValue('Buy flowers');
      await user.clear(editInput);
      await user.type(editInput, 'Buy beautiful flowers');
      await user.keyboard('{Enter}');

      // Verify edit was applied
      await waitFor(() => {
        expect(screen.getByText('Buy beautiful flowers')).toBeInTheDocument();
        expect(screen.queryByText('Buy flowers')).not.toBeInTheDocument();
      });
    });
  });

  // ============================================================================
  // ERROR RECOVERY SCENARIOS
  // ============================================================================

  describe('Error Recovery Scenarios', () => {
    it('handles network interruption gracefully', async () => {
      const user = userEvent.setup();
      
      // Start with working API
      mockGetEvents.mockResolvedValue([]);
      
      render(<App />);

      // Navigate to import form
      const addContactButton = screen.getByRole('button', { name: /add contact/i });
      await user.click(addContactButton);

      // Fill out form
      await user.type(screen.getByLabelText(/contact name/i), 'Test Contact');
      await user.type(screen.getByLabelText(/phone number/i), '+1234567890');
      await user.type(screen.getByLabelText(/event date/i), '2025-12-25');
      await user.type(screen.getByLabelText(/lead time/i), '7');

      // Simulate network failure
      mockImportContacts.mockRejectedValue(new Error('Network error'));

      const submitButton = screen.getByRole('button', { name: /import contact/i });
      await user.click(submitButton);

      // User sees error
      await waitFor(() => {
        expect(screen.getByText('Network error')).toBeInTheDocument();
      });

      // User tries again after network recovers
      mockImportContacts.mockResolvedValue(undefined);
      await user.click(submitButton);

      // Should succeed this time
      await waitFor(() => {
        expect(mockImportContacts).toHaveBeenCalledTimes(2);
      });
    });

    it('handles partial data corruption gracefully', async () => {
      const user = userEvent.setup();
      
      // Mock API returning corrupted data
      const corruptedEvents = [
        {
          id: '1',
          contact_name: 'Valid Contact',
          contact_phone: '+1234567890',
          event_date: '2025-12-25',
          lead_time_days: 7,
          reminder_sent: false,
        },
        {
          // Missing required fields
          id: '2',
          contact_name: '',
          event_date: 'invalid-date',
        },
        {
          id: '3',
          contact_name: 'Another Valid Contact',
          contact_phone: '+0987654321',
          event_date: '2025-11-15',
          lead_time_days: 14,
          reminder_sent: true,
        },
      ];
      
      mockGetEvents.mockResolvedValue(corruptedEvents as IContact[]);
      
      render(<App />);

      // Should only display valid events
      await waitFor(() => {
        expect(screen.getByText('Valid Contact')).toBeInTheDocument();
        expect(screen.getByText('Another Valid Contact')).toBeInTheDocument();
      });

      // Should not crash or display corrupted data
      expect(screen.queryByText('invalid-date')).not.toBeInTheDocument();
    });
  });

  // ============================================================================
  // ACCESSIBILITY SCENARIOS
  // ============================================================================

  describe('Accessibility User Scenarios', () => {
    it('supports screen reader user workflow', async () => {
      const user = userEvent.setup();
      
      const events: IContact[] = [
        {
          id: '1',
          contact_name: 'Mom',
          contact_phone: '+1234567890',
          event_date: '2025-05-12',
          lead_time_days: 14,
          reminder_sent: false,
        },
      ];
      
      mockGetEvents.mockResolvedValue(events);
      
      render(<App />);

      // Verify proper ARIA labels and roles
      expect(screen.getByRole('application')).toBeInTheDocument();
      expect(screen.getByRole('navigation')).toHaveAttribute('aria-label', 'Main navigation');
      expect(screen.getByRole('main')).toBeInTheDocument();

      // Navigate using keyboard only
      await user.tab(); // Focus on first nav button
      expect(screen.getAllByRole('button', { name: /events/i })[0]).toHaveFocus();

      await user.tab(); // Focus on add contact button
      expect(screen.getByRole('button', { name: /add contact/i })).toHaveFocus();

      // Navigate to import form
      await user.keyboard('{Enter}');
      expect(screen.getByText('Add New Contact')).toBeInTheDocument();

      // Tab through form fields
      await user.tab();
      expect(screen.getByLabelText(/contact name/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/phone number/i)).toHaveFocus();

      // Verify form has proper labels and descriptions
      expect(screen.getByLabelText(/contact name/i)).toHaveAttribute('required');
      expect(screen.getByLabelText(/phone number/i)).toHaveAttribute('required');
    });

    it('supports keyboard-only navigation throughout app', async () => {
      const user = userEvent.setup();
      
      const events: IContact[] = [
        {
          id: '1',
          contact_name: 'Test Contact',
          contact_phone: '+1234567890',
          event_date: '2025-12-25',
          lead_time_days: 7,
          reminder_sent: false,
        },
      ];
      
      mockGetEvents.mockResolvedValue(events);
      mockGetChecklists.mockResolvedValue([]);
      
      render(<App />);

      // Wait for events to load
      await waitFor(() => {
        expect(screen.getByText('Test Contact')).toBeInTheDocument();
      });

      // Navigate to event using keyboard
      const eventCard = screen.getByText('Test Contact').closest('.event-list__item');
      if (eventCard) {
        // Focus on the event card and activate it
        (eventCard as HTMLElement).focus();
        await user.keyboard('{Enter}');
      }

      // Should navigate to detail view
      await waitFor(() => {
        expect(screen.getByText('Event Details')).toBeInTheDocument();
      });

      // Tab through action buttons
      await user.tab();
      const firstButton = document.activeElement;
      expect(firstButton).toHaveAttribute('type', 'button');

      // Should be able to activate buttons with keyboard
      await user.keyboard('{Enter}');
      
      // Verify keyboard interaction works
      expect(true).toBe(true); // If we reach here, keyboard navigation works
    });
  });
});
