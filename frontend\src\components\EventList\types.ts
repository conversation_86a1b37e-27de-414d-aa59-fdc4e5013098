/**
 * TypeScript interfaces for EventList component
 * Defines props, state, and data structures for event list functionality
 */

import { IContact } from '../../types/api';

// ============================================================================
// COMPONENT PROPS INTERFACES
// ============================================================================

export interface IEventListProps {
  /** Optional callback when an event is selected */
  onEventSelect?: (event: IContact) => void;
  
  /** Optional callback when refresh is triggered */
  onRefresh?: () => void;
  
  /** Whether the component is in loading state */
  isLoading?: boolean;
  
  /** Optional error message to display */
  error?: string;
  
  /** Optional CSS class name for styling */
  className?: string;
  
  /** Whether to show the refresh button */
  showRefreshButton?: boolean;
  
  /** Whether to show filter and sort controls */
  showControls?: boolean;
  
  /** Maximum number of events to display (for pagination) */
  maxEvents?: number;
}

// ============================================================================
// FILTER AND SORT INTERFACES
// ============================================================================

export type SortField = 'event_date' | 'contact_name' | 'lead_time_days';
export type SortDirection = 'asc' | 'desc';
export type FilterStatus = 'all' | 'pending' | 'sent';

export interface ISortConfig {
  field: SortField;
  direction: SortDirection;
}

export interface IFilterConfig {
  status: FilterStatus;
  searchTerm: string;
  dateRange?: {
    start: string; // YYYY-MM-DD
    end: string;   // YYYY-MM-DD
  };
}

// ============================================================================
// COMPONENT STATE INTERFACES
// ============================================================================

export interface IEventListState {
  events: IContact[];
  filteredEvents: IContact[];
  isLoading: boolean;
  error: string | null;
  sortConfig: ISortConfig;
  filterConfig: IFilterConfig;
  selectedEventId: string | null;
}

// ============================================================================
// DEFAULT VALUES
// ============================================================================

export const DEFAULT_SORT_CONFIG: ISortConfig = {
  field: 'event_date',
  direction: 'asc',
};

export const DEFAULT_FILTER_CONFIG: IFilterConfig = {
  status: 'all',
  searchTerm: '',
  dateRange: undefined,
};

export const DEFAULT_EVENT_LIST_STATE: IEventListState = {
  events: [],
  filteredEvents: [],
  isLoading: false,
  error: null,
  sortConfig: DEFAULT_SORT_CONFIG,
  filterConfig: DEFAULT_FILTER_CONFIG,
  selectedEventId: null,
};

// ============================================================================
// UTILITY TYPES
// ============================================================================

export interface IEventListActions {
  loadEvents: () => Promise<void>;
  refreshEvents: () => Promise<void>;
  sortEvents: (field: SortField, direction?: SortDirection) => void;
  filterEvents: (config: Partial<IFilterConfig>) => void;
  selectEvent: (eventId: string | null) => void;
  clearError: () => void;
}

// ============================================================================
// VALIDATION AND HELPER TYPES
// ============================================================================

export interface IEventDisplayData extends IContact {
  /** Calculated days until event */
  daysUntilEvent: number;
  
  /** Calculated reminder date */
  reminderDate: string;
  
  /** Whether the reminder is overdue */
  isOverdue: boolean;
  
  /** Whether the reminder should be sent today */
  isDueToday: boolean;
  
  /** Formatted display date */
  displayDate: string;
  
  /** Status display text */
  statusText: string;
}

export type EventListError = 
  | 'FETCH_ERROR'
  | 'NETWORK_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

// ============================================================================
// CONSTANTS
// ============================================================================

export const SORT_FIELD_LABELS: Record<SortField, string> = {
  event_date: 'Event Date',
  contact_name: 'Contact Name',
  lead_time_days: 'Lead Time',
};

export const FILTER_STATUS_LABELS: Record<FilterStatus, string> = {
  all: 'All Events',
  pending: 'Pending Reminders',
  sent: 'Sent Reminders',
};

export const EVENT_LIST_MESSAGES = {
  LOADING: 'Loading events...',
  NO_EVENTS: 'No events found. Import some contacts to get started.',
  NO_FILTERED_EVENTS: 'No events match your current filters.',
  ERROR_GENERIC: 'Failed to load events. Please try again.',
  ERROR_NETWORK: 'Network error. Please check your connection and try again.',
  REFRESH_SUCCESS: 'Events refreshed successfully.',
} as const;
