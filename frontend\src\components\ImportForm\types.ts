/**
 * TypeScript interfaces for ImportForm component
 * Defines props, form data, and validation structures
 */

import { IContactData } from '../../types/api';

// ============================================================================
// FORM DATA INTERFACES
// ============================================================================

export interface IImportFormData {
  contact_name: string;
  contact_phone: string;
  event_date: string; // YYYY-MM-DD format
  lead_time_days: number;
}

export interface IFormErrors {
  contact_name?: string;
  contact_phone?: string;
  event_date?: string;
  lead_time_days?: string;
  general?: string;
}

// ============================================================================
// COMPONENT PROPS INTERFACES
// ============================================================================

export interface IImportFormProps {
  /** Callback function called when form is successfully submitted */
  onSubmit: (data: IContactData) => Promise<void>;
  
  /** Optional callback for form submission success */
  onSuccess?: (importedCount: number) => void;
  
  /** Optional callback for form submission error */
  onError?: (error: string) => void;
  
  /** Whether the form is currently submitting */
  isLoading?: boolean;
  
  /** Optional initial form data */
  initialData?: Partial<IImportFormData>;
  
  /** Optional CSS class name for styling */
  className?: string;
  
  /** Whether to show success message after submission */
  showSuccessMessage?: boolean;
  
  /** Whether to reset form after successful submission */
  resetOnSuccess?: boolean;
}

// ============================================================================
// FORM STATE INTERFACES
// ============================================================================

export interface IFormState {
  data: IImportFormData;
  errors: IFormErrors;
  isSubmitting: boolean;
  isValid: boolean;
  touched: {
    contact_name: boolean;
    contact_phone: boolean;
    event_date: boolean;
    lead_time_days: boolean;
  };
}

// ============================================================================
// VALIDATION INTERFACES
// ============================================================================

export interface IValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  min?: number;
  max?: number;
  custom?: (value: any) => string | undefined;
}

export interface IValidationRules {
  contact_name: IValidationRule;
  contact_phone: IValidationRule;
  event_date: IValidationRule;
  lead_time_days: IValidationRule;
}

// ============================================================================
// DEFAULT VALUES
// ============================================================================

export const DEFAULT_FORM_DATA: IImportFormData = {
  contact_name: '',
  contact_phone: '',
  event_date: '',
  lead_time_days: 7,
};

export const DEFAULT_FORM_ERRORS: IFormErrors = {};

export const DEFAULT_TOUCHED = {
  contact_name: false,
  contact_phone: false,
  event_date: false,
  lead_time_days: false,
};

// ============================================================================
// VALIDATION RULES
// ============================================================================

export const VALIDATION_RULES: IValidationRules = {
  contact_name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z\s'-]+$/,
  },
  contact_phone: {
    required: true,
    minLength: 10,
    maxLength: 20,
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
  },
  event_date: {
    required: true,
    custom: (value: string) => {
      if (!value) return 'Event date is required';
      
      const date = new Date(value);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (isNaN(date.getTime())) {
        return 'Please enter a valid date';
      }
      
      if (date < today) {
        return 'Event date cannot be in the past';
      }
      
      // Check if date is more than 2 years in the future
      const maxDate = new Date();
      maxDate.setFullYear(maxDate.getFullYear() + 2);
      
      if (date > maxDate) {
        return 'Event date cannot be more than 2 years in the future';
      }
      
      return undefined;
    },
  },
  lead_time_days: {
    required: true,
    min: 1,
    max: 365,
  },
};
