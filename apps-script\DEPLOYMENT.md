# Google Apps Script Deployment Guide

## 🚀 Quick Deployment Steps

### 1. Initial Setup
1. Open [Google Apps Script Console](https://script.google.com)
2. Click **"New Project"**
3. Name the project: **"Remindar-Backend"**

### 2. Code Deployment
1. **Replace Code.gs content:**
   - Delete default `myFunction()`
   - Copy entire content from `apps-script/Code.gs`
   - Paste into the Apps Script editor

2. **Update Manifest:**
   - Click on `appsscript.json` in the file list
   - Replace content with `apps-script/appsscript.json`
   - This enables Calendar API and sets proper OAuth scopes

### 3. Initialize Database
1. **Run Setup Function:**
   - Select `setupSheets` from function dropdown
   - Click **Run** button
   - **Grant permissions** when prompted:
     - Google Sheets access
     - Google Calendar access
     - External requests

2. **Verify Database Creation:**
   - Check that a new Google Sheet was created
   - Verify it contains two sheets: "Reminders" and "Checklists"
   - Confirm proper column headers are present

### 4. Deploy as Web App
1. **Create Deployment:**
   - Click **Deploy** → **New deployment**
   - Click gear icon → Select **"Web app"**

2. **Configure Settings:**
   - **Description:** "Remindar MVP API v1.0"
   - **Execute as:** Me (<EMAIL>)
   - **Who has access:** Anyone
   - Click **Deploy**

3. **Copy Web App URL:**
   - Copy the provided Web App URL
   - Format: `https://script.google.com/macros/s/{SCRIPT_ID}/exec`
   - **IMPORTANT:** Save this URL for frontend configuration

### 5. Test Deployment
1. **Health Check:**
   - Open Web App URL in browser
   - Should display: "Reminder API is running - OK"

2. **API Endpoint Test:**
   - Use Postman or curl to test endpoints
   - Example health check:
     ```bash
     curl "https://script.google.com/macros/s/{SCRIPT_ID}/exec"
     ```

## 🔧 Configuration for Frontend

### Environment Variables
Add the Web App URL to your frontend environment:

**For Development (.env.local):**
```
REACT_APP_API_URL=https://script.google.com/macros/s/{SCRIPT_ID}/exec
```

**For Production (Vercel/GitHub):**
- Add `REACT_APP_API_URL` to environment variables
- Use the same Web App URL

## 🧪 Testing Endpoints

### 1. Health Check
```bash
curl "https://script.google.com/macros/s/{SCRIPT_ID}/exec"
# Expected: "Reminder API is running - OK"
```

### 2. Import Contacts
```bash
curl -X POST "https://script.google.com/macros/s/{SCRIPT_ID}/exec?action=importContacts" \
  -H "Content-Type: application/json" \
  -d '{
    "data": [{
      "contact_name": "Test User",
      "contact_phone": "+**********",
      "event_date": "2025-12-25",
      "lead_time_days": 7
    }]
  }'
```

### 3. Get Events
```bash
curl "https://script.google.com/macros/s/{SCRIPT_ID}/exec?action=getEvents"
```

### 4. Schedule Reminders
```bash
curl -X POST "https://script.google.com/macros/s/{SCRIPT_ID}/exec?action=scheduleReminders" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 5. Get Checklists
```bash
curl "https://script.google.com/macros/s/{SCRIPT_ID}/exec?action=getChecklists&eventId=YOUR_EVENT_ID"
```

## 🔐 Security Considerations

### OAuth Scopes Required
- `https://www.googleapis.com/auth/script.external_request`
- `https://www.googleapis.com/auth/spreadsheets`
- `https://www.googleapis.com/auth/calendar`

### Access Control
- **Execute as:** Me (ensures your permissions are used)
- **Who has access:** Anyone (required for frontend to access)
- **Note:** This is secure because no sensitive data is exposed

## 🚨 Troubleshooting

### Common Issues

**1. Permission Denied Error**
- **Cause:** OAuth scopes not granted
- **Solution:** Re-run `setupSheets()` and grant all permissions

**2. "Script function not found" Error**
- **Cause:** Function name mismatch or deployment not updated
- **Solution:** Redeploy with latest code

**3. CORS Errors in Frontend**
- **Cause:** Apps Script CORS is automatically handled
- **Solution:** Ensure Web App is deployed with "Anyone" access

**4. Timeout Errors**
- **Cause:** Apps Script execution time limit (6 minutes)
- **Solution:** Optimize functions or implement pagination

### Debugging Steps
1. **Check Execution Transcript:**
   - Apps Script Editor → Executions
   - View logs and error details

2. **Test Individual Functions:**
   - Select function in dropdown
   - Click Run to test locally

3. **Verify Permissions:**
   - Check OAuth consent screen
   - Ensure all required scopes are granted

## 📊 Monitoring & Maintenance

### Daily Checks
- [ ] Health check endpoint responds
- [ ] Google Sheets accessible
- [ ] Calendar integration working

### Weekly Maintenance
- [ ] Review execution logs for errors
- [ ] Check quota usage (Apps Script limits)
- [ ] Verify data integrity in sheets

### Quota Limits (Google Apps Script)
- **Execution time:** 6 minutes per execution
- **Triggers:** 20 time-driven triggers per script
- **Email:** 100 emails per day
- **URL Fetch:** 20,000 calls per day

## 🔄 Updating Deployment

### Code Updates
1. Update code in Apps Script editor
2. **Deploy** → **Manage deployments**
3. Click edit icon on existing deployment
4. **Version:** New version
5. Click **Deploy**

### Rollback Process
1. **Deploy** → **Manage deployments**
2. Select previous version
3. Click **Deploy**

---

**Last Updated:** 2025-07-14  
**Version:** 1.0.0  
**Maintainer:** Development Team
