/**
 * ImportForm Component Styles
 * Responsive design with accessibility considerations
 */

.import-form {
  max-width: 500px;
  margin: 0 auto;
  padding: 2rem;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.import-form__header {
  margin-bottom: 2rem;
  text-align: center;
}

.import-form__title {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
}

.import-form__description {
  margin: 0;
  font-size: 0.875rem;
  color: #666666;
  line-height: 1.4;
}

/* Fields Container */
.import-form__fields {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Individual Field */
.import-form__field {
  display: flex;
  flex-direction: column;
}

.import-form__label {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.import-form__input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background-color: #ffffff;
}

.import-form__input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.import-form__input:disabled {
  background-color: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

.import-form__input--error {
  border-color: #ef4444;
}

.import-form__input--error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Field Help Text */
.import-form__field-help {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #6b7280;
}

/* Error Messages */
.import-form__field-error {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #ef4444;
  display: flex;
  align-items: center;
}

.import-form__field-error::before {
  content: '⚠';
  margin-right: 0.25rem;
  font-size: 0.875rem;
}

.import-form__error--general {
  padding: 0.75rem;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.import-form__error--general::before {
  content: '❌';
  margin-right: 0.5rem;
  font-size: 1rem;
}

/* Success Message */
.import-form__success {
  padding: 0.75rem;
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 6px;
  color: #166534;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.import-form__success::before {
  content: '✅';
  margin-right: 0.5rem;
  font-size: 1rem;
}

/* Actions */
.import-form__actions {
  display: flex;
  justify-content: center;
}

.import-form__submit {
  padding: 0.75rem 2rem;
  background-color: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  min-width: 140px;
}

.import-form__submit:hover:not(:disabled) {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.import-form__submit:active:not(:disabled) {
  transform: translateY(0);
}

.import-form__submit:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.import-form__submit:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Responsive Design */
@media (max-width: 640px) {
  .import-form {
    padding: 1.5rem;
    margin: 1rem;
    border-radius: 6px;
  }

  .import-form__title {
    font-size: 1.25rem;
  }

  .import-form__fields {
    gap: 1.25rem;
  }

  .import-form__input {
    padding: 0.625rem;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .import-form__submit {
    width: 100%;
    padding: 0.875rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .import-form__input {
    border-width: 2px;
  }

  .import-form__input:focus {
    border-width: 3px;
  }

  .import-form__submit {
    border: 2px solid transparent;
  }

  .import-form__submit:focus {
    border-color: #ffffff;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .import-form__input,
  .import-form__submit {
    transition: none;
  }

  .import-form__submit:hover:not(:disabled) {
    transform: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .import-form {
    background-color: #1f2937;
    color: #f9fafb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .import-form__title {
    color: #f9fafb;
  }

  .import-form__description {
    color: #d1d5db;
  }

  .import-form__label {
    color: #e5e7eb;
  }

  .import-form__input {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .import-form__input:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  .import-form__input:disabled {
    background-color: #1f2937;
    color: #6b7280;
  }

  .import-form__field-help {
    color: #9ca3af;
  }

  .import-form__error--general {
    background-color: #1f2937;
    border-color: #ef4444;
    color: #fca5a5;
  }

  .import-form__success {
    background-color: #1f2937;
    border-color: #10b981;
    color: #6ee7b7;
  }
}
