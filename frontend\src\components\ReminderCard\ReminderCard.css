/**
 * ReminderCard Component Styles
 * Responsive design with status-based theming and accessibility features
 */

/* ============================================================================
   BASE CARD STYLES
   ============================================================================ */

.reminder-card {
  position: relative;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.15s ease-in-out;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.reminder-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.reminder-card:focus-within {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

/* Card States */
.reminder-card--selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.reminder-card--loading {
  opacity: 0.7;
  pointer-events: none;
}

.reminder-card--error {
  border-color: #ef4444;
}

.reminder-card--expanded {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Status-based theming */
.reminder-card--pending {
  border-left: 4px solid #f59e0b;
}

.reminder-card--due-today {
  border-left: 4px solid #ef4444;
}

.reminder-card--overdue {
  border-left: 4px solid #dc2626;
  background-color: #fef2f2;
}

.reminder-card--sent {
  border-left: 4px solid #10b981;
  background-color: #f0fdf4;
}

/* ============================================================================
   HEADER SECTION
   ============================================================================ */

.reminder-card__header {
  display: grid;
  grid-template-columns: 2fr 1.5fr auto;
  gap: 1rem;
  align-items: center;
  margin-bottom: 0.75rem;
}

.reminder-card__contact-info {
  min-width: 0; /* Allow text truncation */
}

.reminder-card__contact-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reminder-card__contact-phone {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  font-family: 'Courier New', monospace;
}

.reminder-card__event-info {
  text-align: center;
  min-width: 0;
}

.reminder-card__event-date {
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.reminder-card__countdown {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* ============================================================================
   STATUS INDICATOR
   ============================================================================ */

.reminder-card__status {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
  min-width: 100px;
}

.reminder-card__status--pending {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

.reminder-card__status--due-today {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fca5a5;
}

.reminder-card__status--overdue {
  background-color: #fee2e2;
  color: #991b1b;
  border: 1px solid #f87171;
}

.reminder-card__status--sent {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #6ee7b7;
}

/* ============================================================================
   DETAILS SECTION
   ============================================================================ */

.reminder-card__details {
  background-color: #f8fafc;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  border: 1px solid #e2e8f0;
}

.reminder-card__detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.reminder-card__detail-row:not(:last-child) {
  border-bottom: 1px solid #e2e8f0;
}

.reminder-card__detail-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
}

.reminder-card__detail-value {
  font-size: 0.875rem;
  color: #1f2937;
  font-weight: 400;
}

.reminder-card__detail-value--warning {
  color: #dc2626;
  font-weight: 500;
}

/* ============================================================================
   ACTIONS SECTION
   ============================================================================ */

.reminder-card__actions {
  position: relative;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-top: 0.75rem;
}

.reminder-card__action {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  border: 1px solid transparent;
  text-decoration: none;
  white-space: nowrap;
}

.reminder-card__action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reminder-card__action--primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.reminder-card__action--primary:hover:not(:disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
}

.reminder-card__action--secondary {
  background-color: #f8fafc;
  color: #374151;
  border-color: #d1d5db;
}

.reminder-card__action--secondary:hover:not(:disabled) {
  background-color: #f1f5f9;
  border-color: #9ca3af;
}

.reminder-card__action--danger {
  background-color: #fef2f2;
  color: #dc2626;
  border-color: #fca5a5;
}

.reminder-card__action--danger:hover:not(:disabled) {
  background-color: #fee2e2;
  border-color: #f87171;
}

.reminder-card__action-icon {
  font-size: 1rem;
  line-height: 1;
}

.reminder-card__action-label {
  line-height: 1;
}

/* ============================================================================
   SNOOZE DROPDOWN
   ============================================================================ */

.reminder-card__snooze-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 10;
  margin-top: 0.25rem;
}

.reminder-card__snooze-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.reminder-card__snooze-close {
  background: none;
  border: none;
  font-size: 1rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out;
}

.reminder-card__snooze-close:hover {
  color: #374151;
}

.reminder-card__snooze-options {
  padding: 0.5rem;
}

.reminder-card__snooze-option {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.75rem;
  border: none;
  background: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
  margin-bottom: 0.25rem;
}

.reminder-card__snooze-option:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.reminder-card__snooze-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reminder-card__snooze-option:last-child {
  margin-bottom: 0;
}

.reminder-card__snooze-option-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 0.125rem;
}

.reminder-card__snooze-option-description {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
}

/* ============================================================================
   ERROR DISPLAY
   ============================================================================ */

.reminder-card__error {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
}

.reminder-card__error-message {
  font-size: 0.875rem;
  color: #dc2626;
  flex: 1;
}

.reminder-card__error-dismiss {
  background: none;
  border: none;
  font-size: 1rem;
  color: #dc2626;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.15s ease-in-out;
  margin-left: 0.5rem;
}

.reminder-card__error-dismiss:hover {
  background-color: #fee2e2;
}

/* ============================================================================
   LOADING OVERLAY
   ============================================================================ */

.reminder-card__loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  z-index: 20;
}

.reminder-card__loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: reminder-card-spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

.reminder-card__loading-message {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

@keyframes reminder-card-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============================================================================
   EXPAND/COLLAPSE BUTTON
   ============================================================================ */

.reminder-card__expand-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: none;
  border: none;
  font-size: 0.875rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.15s ease-in-out;
  z-index: 5;
}

.reminder-card__expand-button:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.reminder-card__expand-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  .reminder-card {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .reminder-card__header {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    text-align: left;
  }

  .reminder-card__contact-name {
    font-size: 1rem;
  }

  .reminder-card__event-info {
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .reminder-card__status {
    min-width: auto;
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
  }

  .reminder-card__actions {
    flex-direction: column;
    gap: 0.375rem;
  }

  .reminder-card__action {
    justify-content: center;
    padding: 0.625rem 0.75rem;
  }

  .reminder-card__snooze-dropdown {
    position: fixed;
    top: 50%;
    left: 1rem;
    right: 1rem;
    transform: translateY(-50%);
    max-height: 80vh;
    overflow-y: auto;
  }

  .reminder-card__details {
    padding: 0.5rem;
  }

  .reminder-card__detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    padding: 0.375rem 0;
  }
}

@media (max-width: 480px) {
  .reminder-card {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .reminder-card__header {
    gap: 0.5rem;
  }

  .reminder-card__contact-name {
    font-size: 0.9375rem;
  }

  .reminder-card__contact-phone {
    font-size: 0.8125rem;
  }

  .reminder-card__event-date {
    font-size: 0.875rem;
  }

  .reminder-card__countdown {
    font-size: 0.6875rem;
  }

  .reminder-card__expand-button {
    top: 0.25rem;
    right: 0.25rem;
  }
}

/* ============================================================================
   ACCESSIBILITY ENHANCEMENTS
   ============================================================================ */

@media (prefers-reduced-motion: reduce) {
  .reminder-card,
  .reminder-card__action,
  .reminder-card__expand-button,
  .reminder-card__snooze-close,
  .reminder-card__snooze-option,
  .reminder-card__error-dismiss {
    transition: none;
  }

  .reminder-card:hover {
    transform: none;
  }

  .reminder-card__loading-spinner {
    animation: none;
  }
}

@media (prefers-color-scheme: dark) {
  .reminder-card {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .reminder-card--selected {
    background-color: #1e3a8a;
    border-color: #3b82f6;
  }

  .reminder-card--overdue {
    background-color: #7f1d1d;
  }

  .reminder-card--sent {
    background-color: #064e3b;
  }

  .reminder-card__contact-name {
    color: #f9fafb;
  }

  .reminder-card__contact-phone,
  .reminder-card__countdown {
    color: #d1d5db;
  }

  .reminder-card__event-date {
    color: #e5e7eb;
  }

  .reminder-card__details {
    background-color: #374151;
    border-color: #4b5563;
  }

  .reminder-card__detail-label {
    color: #d1d5db;
  }

  .reminder-card__detail-value {
    color: #f9fafb;
  }

  .reminder-card__action--secondary {
    background-color: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }

  .reminder-card__action--secondary:hover:not(:disabled) {
    background-color: #4b5563;
    border-color: #6b7280;
  }

  .reminder-card__snooze-dropdown {
    background: #1f2937;
    border-color: #374151;
  }

  .reminder-card__snooze-header {
    border-color: #374151;
    color: #e5e7eb;
  }

  .reminder-card__snooze-option:hover:not(:disabled) {
    background-color: #374151;
  }

  .reminder-card__snooze-option-label {
    color: #f9fafb;
  }

  .reminder-card__snooze-option-description {
    color: #d1d5db;
  }

  .reminder-card__expand-button {
    color: #d1d5db;
  }

  .reminder-card__expand-button:hover {
    color: #f9fafb;
    background-color: #374151;
  }
}
