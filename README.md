# Reminder & Gifting MVP

A TypeScript React-based reminder system with Google Apps Script backend for managing gift reminders and calendar integration.

## 🚀 Quick Start

### Prerequisites
- **Node.js**: v18+ (verified with v18.x)
- **VS Code** with extensions:
  - JavaScript/TypeScript Nightly
  - Google Apps Script (by Google)
  - Live Server
  - Prettier
  - ESLint
- **Google Account** for Apps Script and Sheets access

### Installation

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd Remindar
   ```

2. **Frontend setup**
   ```bash
   cd frontend
   npm install          # Install dependencies
   npm start           # Start development server (http://localhost:3000)
   ```

3. **Apps Script setup**
   - Open [Google Apps Script console](https://script.google.com)
   - Create new project named "Remindar-Backend"
   - Copy `apps-script/Code.gs` and `apps-script/appsscript.json`
   - Run `setupSheets()` function once to initialize database
   - Deploy as web app with public access

## 📁 Current Project Structure

```
Remindar/
├── .github/
│   └── workflows/              # CI/CD automation
│       ├── pr.yaml            # Pull request validation
│       ├── deploy-develop.yaml # Development deployment
│       └── deploy-main.yaml   # Production deployment
├── apps-script/               # Google Apps Script backend
│   ├── Code.gs               # Core server functions & API endpoints
│   └── appsscript.json       # Manifest with OAuth scopes
├── frontend/                  # React TypeScript application
│   ├── public/               # Static assets
│   ├── src/
│   │   ├── components/       # UI components (ready for development)
│   │   ├── services/         # API service layer (ready for development)
│   │   ├── App.tsx          # Main application component
│   │   ├── index.tsx        # Application entry point
│   │   └── react-app-env.d.ts # TypeScript declarations
│   ├── .eslintrc.js         # Code quality rules
│   ├── .prettierrc          # Code formatting configuration
│   ├── package.json         # Dependencies & scripts
│   └── tsconfig.json        # TypeScript configuration
├── .editorconfig             # Consistent coding style
├── .gitignore               # Git ignore patterns
├── PDP.md                   # Project Development Protocol
├── PRD.md                   # Product Requirements Document
├── README.md                # This file
└── tasks.md                 # Task management & tracking
```

## 🛡️ Development Guardrails & Quality Gates

### 🚫 CRITICAL RULES - NON-NEGOTIABLE
1. **NO DUPLICATE CODE**: Before creating any component/function, check if similar functionality exists
2. **NO SCOPE CREEP**: Stick to MVP features defined in PRD.md - ask before adding new features
3. **NO BREAKING CHANGES**: All changes must maintain backward compatibility
4. **NO DIRECT PACKAGE.JSON EDITS**: Use `npm install/uninstall` commands only
5. **NO CONSOLE.LOG IN PRODUCTION**: Remove all debug statements before committing
6. **NO HARDCODED VALUES**: Use environment variables or configuration files

### 📋 Pre-Development Checklist
Before starting any new feature:
- [ ] Check existing components in `frontend/src/components/`
- [ ] Review API endpoints in `apps-script/Code.gs`
- [ ] Verify feature is in MVP scope (PRD.md)
- [ ] Create/update task in tasks.md
- [ ] Check for similar functionality in codebase

### 🏗️ Component Creation Decision Tree
```
Need new UI element?
├── Does similar component exist?
│   ├── YES → Extend existing component with props
│   └── NO → Continue to next check
├── Is it reusable across multiple views?
│   ├── YES → Create in /components with proper TypeScript interfaces
│   └── NO → Create as local component within parent
└── Does it handle business logic?
    ├── YES → Separate into component + service
    └── NO → Create pure UI component
```

### 📝 Naming Conventions (MANDATORY)
- **Components**: PascalCase (`ImportForm.tsx`, `ReminderCard.tsx`)
- **Files**: kebab-case for utilities (`date-utils.ts`, `api-client.ts`)
- **Functions**: camelCase (`importContacts`, `scheduleReminders`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`, `SHEET_NAME_REMINDERS`)
- **Interfaces**: PascalCase with 'I' prefix (`IContact`, `IReminderData`)

## 🔧 Development Workflow

### Frontend Development
```bash
cd frontend
npm start              # Start development server (http://localhost:3000)
npm test               # Run tests with coverage
npm run build          # Build for production
npx prettier --write "src/**/*.{ts,tsx}"  # Format code
npx eslint "src/**/*.{ts,tsx}" --fix      # Fix linting issues
```

### Apps Script Development
1. **Setup**: Use VS Code with Google Apps Script extension
2. **Testing**: Deploy as web app and test endpoints
3. **Validation**: Run `setupSheets()` function after changes
4. **Deployment**: Update web app deployment in Apps Script console

### 🔍 Code Quality Checkpoints
**Before Every Commit:**
- [ ] TypeScript compilation passes (`npx tsc --noEmit`)
- [ ] ESLint passes with zero warnings (`npx eslint src/ --max-warnings 0`)
- [ ] Prettier formatting applied (`npx prettier --check src/`)
- [ ] Tests pass with >80% coverage (`npm test -- --coverage`)
- [ ] No console.log statements in production code
- [ ] All imports are used and properly typed

## 📋 MVP Features Status

### ✅ Infrastructure Complete
- [x] TypeScript React setup with proper configuration
- [x] Google Apps Script backend with core functions
- [x] CI/CD pipeline with quality gates
- [x] Development environment and tooling

### 🚧 In Development
- [ ] Contact import and management UI
- [ ] Reminder scheduling with lead times
- [ ] Google Calendar integration testing
- [ ] Gift planning checklists interface
- [ ] Browser notifications system
- [ ] Responsive UI implementation

## 🔗 API Endpoints (Implemented)

| Method | Endpoint | Function | Status | Validation |
|--------|----------|----------|---------|------------|
| GET | `/?` | `doGet()` | ✅ | Health check |
| POST | `/?action=importContacts` | `importContacts()` | ✅ | Data validation |
| GET | `/?action=getEvents` | `getEvents()` | ✅ | Sheet exists check |
| POST | `/?action=scheduleReminders` | `scheduleReminders()` | ✅ | Calendar permissions |
| GET | `/?action=getChecklists&eventId=<id>` | `getChecklists()` | ✅ | Event ID validation |

## 🧪 Testing Strategy & Requirements

### Test Coverage Requirements
- **Minimum Coverage**: 80% for all new code
- **Component Tests**: Every component must have unit tests
- **API Tests**: All endpoints must have integration tests
- **E2E Tests**: Critical user flows must be covered

### Testing Commands
```bash
cd frontend
npm test                    # Run tests in watch mode
npm test -- --coverage     # Run with coverage report
npm test -- --watchAll=false  # Run once (CI mode)
```

### Testing Checklist (Before PR)
- [ ] All new components have corresponding `.test.tsx` files
- [ ] API service functions have unit tests
- [ ] Error handling scenarios are tested
- [ ] TypeScript types are properly tested
- [ ] No test files are skipped or disabled

## 🚀 Deployment Pipeline

### Automated Deployments
- **PR Validation**: Runs on every pull request
  - Code quality checks (ESLint, Prettier, TypeScript)
  - Test suite execution with coverage
  - Security audit and sensitive data scan
- **Development**: Auto-deploy to staging on `develop` branch push
- **Production**: Auto-deploy to production on `main` branch push

### Manual Deployment Steps
```bash
# Frontend (if needed)
cd frontend
npm run build
# Deploy build/ folder to hosting platform

# Backend (Google Apps Script)
1. Open Apps Script console
2. Deploy → New deployment → Web app
3. Execute as: Me, Access: Anyone
4. Copy web app URL for frontend configuration
```

### Deployment Validation
- [ ] Health check endpoint responds correctly
- [ ] All API endpoints return expected responses
- [ ] Frontend loads without console errors
- [ ] Calendar integration permissions work
- [ ] Google Sheets access is functional

## 📖 Documentation Hierarchy

### Primary Documentation
- **[PRD.md](./PRD.md)** - Complete product requirements and technical specifications
- **[PDP.md](./PDP.md)** - Development protocol, progress tracking, and architecture decisions
- **[tasks.md](./tasks.md)** - Task management, sprint planning, and progress tracking
- **[README.md](./README.md)** - This file: setup, development guidelines, and project overview

### Code Documentation
- **Inline Comments**: Required for complex business logic
- **TypeScript Interfaces**: Document all data structures
- **API Documentation**: JSDoc comments for all functions
- **Component Props**: TypeScript interfaces with descriptions

## 🤝 Contributing Guidelines

### Branch Strategy
```
main (production)
├── develop (integration)
│   ├── feature/import-form
│   ├── feature/reminder-card
│   └── feature/calendar-integration
└── hotfix/critical-bug-fix
```

### Commit Message Format (Conventional Commits)
```
type(scope): description

feat(components): add ImportForm with validation
fix(api): resolve calendar permission issue
docs(readme): update deployment instructions
test(services): add unit tests for api client
```

### Pull Request Checklist
- [ ] Branch is up to date with `develop`
- [ ] All tests pass locally
- [ ] Code follows established patterns
- [ ] No duplicate functionality created
- [ ] Documentation updated if needed
- [ ] PR description explains changes clearly

### Code Review Criteria
1. **Functionality**: Does it work as intended?
2. **Code Quality**: Follows established patterns and conventions?
3. **Performance**: No unnecessary re-renders or API calls?
4. **Security**: No sensitive data exposure or vulnerabilities?
5. **Maintainability**: Easy to understand and modify?
6. **Testing**: Adequate test coverage for new code?

## ⚠️ Common Pitfalls & Prevention

### API Integration Issues
- **Problem**: CORS errors with Apps Script
- **Prevention**: Ensure proper deployment settings and test with actual URLs
- **Solution**: Check Apps Script deployment permissions and web app settings

### State Management Issues
- **Problem**: Unnecessary re-renders and performance issues
- **Prevention**: Use React.memo, useMemo, and useCallback appropriately
- **Solution**: Profile components and optimize render cycles

### Data Validation Issues
- **Problem**: Invalid data causing runtime errors
- **Prevention**: Implement TypeScript interfaces and runtime validation
- **Solution**: Add comprehensive error boundaries and input validation

### Dependency Management
- **Problem**: Version conflicts and security vulnerabilities
- **Prevention**: Regular `npm audit` and dependency updates
- **Solution**: Use exact versions for critical dependencies

## 📄 License & Support

**License**: MIT License - see LICENSE file for details
**Support**: Create issues in GitHub repository for bugs and feature requests
**Maintainer**: Development team following Extreme Programming practices
