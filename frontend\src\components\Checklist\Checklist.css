/**
 * Checklist Component Styles
 * Responsive design with CRUD operations and accessibility features
 */

/* ============================================================================
   BASE CHECKLIST STYLES
   ============================================================================ */

.checklist {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: all 0.15s ease-in-out;
}

.checklist--loading {
  opacity: 0.7;
  pointer-events: none;
}

.checklist--error {
  border-color: #ef4444;
}

.checklist--processing {
  opacity: 0.9;
}

/* ============================================================================
   HEADER SECTION
   ============================================================================ */

.checklist__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.checklist__title-section {
  flex: 1;
  min-width: 0;
}

.checklist__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.checklist__stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.checklist__stats-text {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.checklist__progress-bar {
  width: 100%;
  height: 0.5rem;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  overflow: hidden;
}

.checklist__progress-fill {
  height: 100%;
  background-color: #10b981;
  border-radius: 0.25rem;
  transition: width 0.3s ease-in-out;
}

/* Progress bar width using CSS custom properties */
.checklist__progress-fill[data-progress="0"] { width: 0%; }
.checklist__progress-fill[data-progress="10"] { width: 10%; }
.checklist__progress-fill[data-progress="20"] { width: 20%; }
.checklist__progress-fill[data-progress="30"] { width: 30%; }
.checklist__progress-fill[data-progress="40"] { width: 40%; }
.checklist__progress-fill[data-progress="50"] { width: 50%; }
.checklist__progress-fill[data-progress="60"] { width: 60%; }
.checklist__progress-fill[data-progress="70"] { width: 70%; }
.checklist__progress-fill[data-progress="80"] { width: 80%; }
.checklist__progress-fill[data-progress="90"] { width: 90%; }
.checklist__progress-fill[data-progress="100"] { width: 100%; }

.checklist__filter {
  flex-shrink: 0;
}

.checklist__filter-select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: white;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.15s ease-in-out;
}

.checklist__filter-select:hover {
  border-color: #9ca3af;
}

.checklist__filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* ============================================================================
   ACTIONS SECTION
   ============================================================================ */

.checklist__actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.checklist__action {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  border: 1px solid transparent;
  text-decoration: none;
  white-space: nowrap;
}

.checklist__action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checklist__action--primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.checklist__action--primary:hover:not(:disabled) {
  background-color: #2563eb;
  border-color: #2563eb;
}

.checklist__action--secondary {
  background-color: #f8fafc;
  color: #374151;
  border-color: #d1d5db;
}

.checklist__action--secondary:hover:not(:disabled) {
  background-color: #f1f5f9;
  border-color: #9ca3af;
}

.checklist__action-icon {
  font-size: 1rem;
  line-height: 1;
}

.checklist__action-label {
  line-height: 1;
}

/* ============================================================================
   ADD FORM SECTION
   ============================================================================ */

.checklist__add-form {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.checklist__add-input-group {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.checklist__add-input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out;
}

.checklist__add-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.checklist__add-input:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.checklist__add-actions {
  display: flex;
  gap: 0.5rem;
}

.checklist__add-button {
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  border: 1px solid transparent;
  white-space: nowrap;
}

.checklist__add-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checklist__add-button--primary {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
}

.checklist__add-button--primary:hover:not(:disabled) {
  background-color: #059669;
  border-color: #059669;
}

.checklist__add-button--secondary {
  background-color: #f8fafc;
  color: #374151;
  border-color: #d1d5db;
}

.checklist__add-button--secondary:hover:not(:disabled) {
  background-color: #f1f5f9;
  border-color: #9ca3af;
}

.checklist__add-hint {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

/* ============================================================================
   ITEMS LIST
   ============================================================================ */

.checklist__items {
  list-style: none;
  padding: 0;
  margin: 0;
}

.checklist__items li {
  margin-bottom: 0.5rem;
}

.checklist__items li:last-child {
  margin-bottom: 0;
}

.checklist__item {
  position: relative;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.75rem;
  transition: all 0.15s ease-in-out;
}

.checklist__item:hover {
  border-color: #d1d5db;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.checklist__item--completed {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

.checklist__item--editing {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.checklist__item--processing {
  opacity: 0.7;
}

.checklist__item--optimistic {
  border-style: dashed;
}

.checklist__item-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.checklist__item-checkbox {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.checklist__checkbox {
  width: 1rem;
  height: 1rem;
  cursor: pointer;
}

.checklist__checkbox:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.checklist__item-text {
  flex: 1;
  min-width: 0;
}

.checklist__item-label {
  font-size: 0.875rem;
  color: #374151;
  line-height: 1.5;
  word-wrap: break-word;
}

.checklist__item-label--completed {
  text-decoration: line-through;
  color: #6b7280;
}

.checklist__item-actions {
  display: flex;
  gap: 0.25rem;
  flex-shrink: 0;
  margin-top: -0.125rem;
}

.checklist__item-action {
  background: none;
  border: none;
  padding: 0.25rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #6b7280;
  transition: all 0.15s ease-in-out;
  line-height: 1;
}

.checklist__item-action:hover:not(:disabled) {
  background-color: #f3f4f6;
  color: #374151;
}

.checklist__item-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ============================================================================
   EDIT FORM
   ============================================================================ */

.checklist__edit-form {
  width: 100%;
}

.checklist__edit-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
  transition: border-color 0.15s ease-in-out;
}

.checklist__edit-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.checklist__edit-input:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.checklist__edit-actions {
  display: flex;
  gap: 0.5rem;
}

.checklist__edit-button {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  border: 1px solid transparent;
}

.checklist__edit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checklist__edit-button--save {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
}

.checklist__edit-button--save:hover:not(:disabled) {
  background-color: #059669;
  border-color: #059669;
}

.checklist__edit-button--cancel {
  background-color: #f8fafc;
  color: #374151;
  border-color: #d1d5db;
}

.checklist__edit-button--cancel:hover:not(:disabled) {
  background-color: #f1f5f9;
  border-color: #9ca3af;
}

/* ============================================================================
   LOADING AND EMPTY STATES
   ============================================================================ */

.checklist__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.checklist__loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: checklist-spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

.checklist__loading-message {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

@keyframes checklist-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.checklist__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.checklist__empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.checklist__empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.checklist__empty-message {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
  max-width: 300px;
}

.checklist__empty-action {
  padding: 0.5rem 1rem;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.checklist__empty-action:hover:not(:disabled) {
  background-color: #2563eb;
}

.checklist__empty-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ============================================================================
   ERROR DISPLAY
   ============================================================================ */

.checklist__error {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
}

.checklist__error-message {
  font-size: 0.875rem;
  color: #dc2626;
  flex: 1;
}

.checklist__error-dismiss {
  background: none;
  border: none;
  font-size: 1rem;
  color: #dc2626;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.15s ease-in-out;
  margin-left: 0.5rem;
}

.checklist__error-dismiss:hover {
  background-color: #fee2e2;
}

/* ============================================================================
   ITEM LOADING INDICATOR
   ============================================================================ */

.checklist__item-loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
}

.checklist__item-spinner {
  width: 1rem;
  height: 1rem;
  border: 1px solid #e5e7eb;
  border-top: 1px solid #3b82f6;
  border-radius: 50%;
  animation: checklist-spin 1s linear infinite;
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  .checklist {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .checklist__header {
    flex-direction: column;
    gap: 0.75rem;
  }

  .checklist__title {
    font-size: 1.125rem;
  }

  .checklist__filter {
    align-self: stretch;
  }

  .checklist__filter-select {
    width: 100%;
  }

  .checklist__actions {
    flex-direction: column;
    gap: 0.375rem;
  }

  .checklist__action {
    justify-content: center;
    width: 100%;
  }

  .checklist__add-input-group {
    flex-direction: column;
  }

  .checklist__add-actions {
    justify-content: stretch;
  }

  .checklist__add-button {
    flex: 1;
  }

  .checklist__item-content {
    gap: 0.5rem;
  }

  .checklist__item-actions {
    flex-direction: column;
    gap: 0.125rem;
  }

  .checklist__edit-actions {
    flex-direction: column;
  }

  .checklist__edit-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .checklist {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .checklist__title {
    font-size: 1rem;
  }

  .checklist__stats-text {
    font-size: 0.8125rem;
  }

  .checklist__item {
    padding: 0.5rem;
  }

  .checklist__item-content {
    gap: 0.375rem;
  }

  .checklist__item-label {
    font-size: 0.8125rem;
  }

  .checklist__empty {
    padding: 1.5rem 1rem;
  }

  .checklist__empty-icon {
    font-size: 2.5rem;
  }

  .checklist__empty-title {
    font-size: 1rem;
  }

  .checklist__empty-message {
    font-size: 0.8125rem;
  }
}

/* ============================================================================
   ACCESSIBILITY ENHANCEMENTS
   ============================================================================ */

@media (prefers-reduced-motion: reduce) {
  .checklist,
  .checklist__action,
  .checklist__add-button,
  .checklist__edit-button,
  .checklist__item-action,
  .checklist__empty-action,
  .checklist__error-dismiss,
  .checklist__progress-fill {
    transition: none;
  }

  .checklist__loading-spinner,
  .checklist__item-spinner {
    animation: none;
  }
}

@media (prefers-color-scheme: dark) {
  .checklist {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }

  .checklist__title {
    color: #f9fafb;
  }

  .checklist__stats-text {
    color: #d1d5db;
  }

  .checklist__progress-bar {
    background-color: #374151;
  }

  .checklist__filter-select {
    background-color: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .checklist__filter-select:hover {
    border-color: #6b7280;
  }

  .checklist__action--secondary {
    background-color: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }

  .checklist__action--secondary:hover:not(:disabled) {
    background-color: #4b5563;
    border-color: #6b7280;
  }

  .checklist__add-form {
    background-color: #374151;
    border-color: #4b5563;
  }

  .checklist__add-input,
  .checklist__edit-input {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #f9fafb;
  }

  .checklist__add-input:focus,
  .checklist__edit-input:focus {
    border-color: #3b82f6;
  }

  .checklist__add-button--secondary,
  .checklist__edit-button--cancel {
    background-color: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }

  .checklist__add-button--secondary:hover:not(:disabled),
  .checklist__edit-button--cancel:hover:not(:disabled) {
    background-color: #4b5563;
    border-color: #6b7280;
  }

  .checklist__item {
    background: #1f2937;
    border-color: #374151;
  }

  .checklist__item:hover {
    border-color: #4b5563;
  }

  .checklist__item--completed {
    background-color: #064e3b;
    border-color: #065f46;
  }

  .checklist__item-label {
    color: #f9fafb;
  }

  .checklist__item-label--completed {
    color: #d1d5db;
  }

  .checklist__item-action {
    color: #d1d5db;
  }

  .checklist__item-action:hover:not(:disabled) {
    background-color: #374151;
    color: #f9fafb;
  }

  .checklist__empty-title {
    color: #f9fafb;
  }

  .checklist__empty-message {
    color: #d1d5db;
  }

  .checklist__add-hint {
    color: #d1d5db;
  }

  .checklist__loading-message {
    color: #d1d5db;
  }
}
