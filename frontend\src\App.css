/**
 * Main App Styles
 * Integrated layout with navigation, views, and responsive design
 */

/* ============================================================================
   BASE APP STYLES
   ============================================================================ */

.app {
  min-height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #1f2937;
  position: relative;
}

/* ============================================================================
   NAVIGATION STYLES
   ============================================================================ */

.app__navigation {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.app__nav-brand {
  flex: 1;
  min-width: 0;
}

.app__title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.app__subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.app__nav-buttons {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.app__nav-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-decoration: none;
  white-space: nowrap;
}

.app__nav-button:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.app__nav-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px #3b82f6;
}

.app__nav-button--active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.app__nav-button--active:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* ============================================================================
   MAIN CONTENT STYLES
   ============================================================================ */

.app__main {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
}

.app__view {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.app__view-header {
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.app__view-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.app__view-description {
  font-size: 1rem;
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.app__view-content {
  padding: 2rem;
}

/* ============================================================================
   DETAIL VIEW LAYOUT
   ============================================================================ */

.app__detail-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.app__detail-layout:has(.app__detail-sidebar) {
  grid-template-columns: 2fr 1fr;
}

.app__detail-main {
  min-width: 0;
}

.app__detail-sidebar {
  min-width: 0;
}

/* ============================================================================
   ERROR HANDLING
   ============================================================================ */

.app__error {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
  max-width: 400px;
  animation: app-slide-in 0.3s ease-out;
}

.app__error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.app__error-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.app__error-message {
  font-size: 0.875rem;
  color: #dc2626;
  flex: 1;
  line-height: 1.4;
}

.app__error-dismiss {
  background: none;
  border: none;
  font-size: 1.125rem;
  color: #dc2626;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.15s ease-in-out;
  flex-shrink: 0;
}

.app__error-dismiss:hover {
  background-color: #fee2e2;
}

@keyframes app-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ============================================================================
   LOADING OVERLAY
   ============================================================================ */

.app__loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.app__loading-content {
  background: white;
  border-radius: 0.5rem;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.app__loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: app-spin 1s linear infinite;
}

.app__loading-message {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

@keyframes app-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 1024px) {
  .app__detail-layout:has(.app__detail-sidebar) {
    grid-template-columns: 1fr;
  }

  .app__detail-sidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .app__navigation {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .app__nav-brand {
    text-align: center;
  }

  .app__title {
    font-size: 1.25rem;
  }

  .app__subtitle {
    font-size: 0.8125rem;
  }

  .app__nav-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .app__nav-button {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .app__main {
    padding: 1rem;
  }

  .app__view-header {
    padding: 1.5rem 1rem 1rem 1rem;
  }

  .app__view-title {
    font-size: 1.5rem;
  }

  .app__view-description {
    font-size: 0.875rem;
  }

  .app__view-content {
    padding: 1rem;
  }

  .app__detail-layout {
    gap: 1rem;
  }

  .app__error {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    max-width: none;
  }

  .app__loading-content {
    margin: 1rem;
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .app__navigation {
    padding: 0.75rem;
  }

  .app__title {
    font-size: 1.125rem;
  }

  .app__subtitle {
    font-size: 0.75rem;
  }

  .app__nav-buttons {
    gap: 0.375rem;
  }

  .app__nav-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
    min-width: 100px;
  }

  .app__main {
    padding: 0.75rem;
  }

  .app__view-header {
    padding: 1rem 0.75rem 0.75rem 0.75rem;
  }

  .app__view-title {
    font-size: 1.25rem;
  }

  .app__view-description {
    font-size: 0.8125rem;
  }

  .app__view-content {
    padding: 0.75rem;
  }

  .app__error-content {
    padding: 0.75rem;
    gap: 0.5rem;
  }

  .app__error-message {
    font-size: 0.8125rem;
  }

  .app__loading-content {
    padding: 1rem;
  }

  .app__loading-spinner {
    width: 2rem;
    height: 2rem;
    border-width: 2px;
  }

  .app__loading-message {
    font-size: 0.8125rem;
  }
}

/* ============================================================================
   ACCESSIBILITY ENHANCEMENTS
   ============================================================================ */

@media (prefers-reduced-motion: reduce) {
  .app__nav-button,
  .app__error-dismiss {
    transition: none;
  }

  .app__loading-spinner {
    animation: none;
  }

  @keyframes app-slide-in {
    from, to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes app-spin {
    from, to {
      transform: rotate(0deg);
    }
  }
}

@media (prefers-color-scheme: dark) {
  .app {
    background-color: #111827;
    color: #f9fafb;
  }

  .app__navigation {
    background: #1f2937;
    border-color: #374151;
  }

  .app__title {
    color: #f9fafb;
  }

  .app__subtitle {
    color: #d1d5db;
  }

  .app__nav-button {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }

  .app__nav-button:hover {
    background-color: #4b5563;
    border-color: #6b7280;
  }

  .app__nav-button--active {
    background-color: #3b82f6;
    border-color: #3b82f6;
  }

  .app__view {
    background: #1f2937;
  }

  .app__view-header {
    border-color: #374151;
  }

  .app__loading-content {
    background: #1f2937;
    color: #f9fafb;
  }

  .app__loading-message {
    color: #d1d5db;
  }

  .app__loading-spinner {
    border-color: #374151;
    border-top-color: #3b82f6;
  }
}

/* ============================================================================
   FOCUS MANAGEMENT
   ============================================================================ */

.app__nav-button:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.app__error-dismiss:focus-visible {
  outline: 2px solid #dc2626;
  outline-offset: 2px;
}

/* ============================================================================
   PRINT STYLES
   ============================================================================ */

@media print {
  .app__navigation,
  .app__error,
  .app__loading-overlay {
    display: none;
  }

  .app__main {
    padding: 0;
    max-width: none;
  }

  .app__view {
    box-shadow: none;
    border: 1px solid #000;
  }

  .app__view-header {
    background: none !important;
    color: #000 !important;
    border-bottom: 2px solid #000;
  }
}
