/**
 * Reminder & Gifting MVP - Google Apps Script Backend
 * Main server-side functions for handling reminders and calendar integration
 */

// Configuration constants
const SHEET_NAME_REMINDERS = 'Reminders';
const SHEET_NAME_CHECKLISTS = 'Checklists';

/**
 * Health check endpoint
 * @param {Object} e - Event object from doGet
 * @returns {ContentService.TextOutput} Simple OK response
 */
function doGet(e) {
  return ContentService.createTextOutput('Reminder API is running - OK')
    .setMimeType(ContentService.MimeType.TEXT);
}

/**
 * Main POST handler for API endpoints
 * @param {Object} e - Event object containing request data
 * @returns {ContentService.TextOutput} JSON response
 */
function doPost(e) {
  try {
    const requestData = JSON.parse(e.postData.contents);
    const action = e.parameter.action;
    
    let response;
    
    switch (action) {
      case 'importContacts':
        response = importContacts(requestData.data);
        break;
      case 'scheduleReminders':
        response = scheduleReminders();
        break;
      default:
        response = { error: { code: 'INVALID_ACTION', message: 'Unknown action: ' + action } };
    }
    
    return ContentService.createTextOutput(JSON.stringify(response))
      .setMimeType(ContentService.MimeType.JSON);
      
  } catch (error) {
    Logger.log('Error in doPost: ' + error.toString());
    const errorResponse = {
      error: {
        code: 'SERVER_ERROR',
        message: 'Internal server error: ' + error.message
      }
    };
    
    return ContentService.createTextOutput(JSON.stringify(errorResponse))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Import contacts data into Reminders sheet
 * @param {Array} contactsData - Array of contact objects
 * @returns {Object} Success response with import count
 */
function importContacts(contactsData) {
  try {
    const sheet = getOrCreateSheet(SHEET_NAME_REMINDERS);
    
    // Ensure headers exist
    const headers = ['id', 'contact_name', 'contact_phone', 'event_date', 'lead_time_days', 'reminder_sent'];
    if (sheet.getLastRow() === 0) {
      sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
    }
    
    let importedCount = 0;
    
    contactsData.forEach(contact => {
      if (validateContactData(contact)) {
        const id = Utilities.getUuid();
        const rowData = [
          id,
          contact.contact_name,
          contact.contact_phone,
          contact.event_date,
          contact.lead_time_days,
          false
        ];
        
        sheet.appendRow(rowData);
        importedCount++;
      }
    });
    
    return {
      success: true,
      importedCount: importedCount
    };
    
  } catch (error) {
    Logger.log('Error in importContacts: ' + error.toString());
    return {
      error: {
        code: 'IMPORT_ERROR',
        message: 'Failed to import contacts: ' + error.message
      }
    };
  }
}

/**
 * Get all events/reminders
 * @returns {Object} Array of events or error
 */
function getEvents() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAME_REMINDERS);
    
    if (!sheet || sheet.getLastRow() <= 1) {
      return [];
    }
    
    const data = sheet.getDataRange().getValues();
    const headers = data[0];
    const events = [];
    
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const event = {};
      
      headers.forEach((header, index) => {
        event[header] = row[index];
      });
      
      events.push(event);
    }
    
    return events;
    
  } catch (error) {
    Logger.log('Error in getEvents: ' + error.toString());
    return {
      error: {
        code: 'FETCH_ERROR',
        message: 'Failed to fetch events: ' + error.message
      }
    };
  }
}

/**
 * Scan for due reminders and create calendar events
 * @returns {Object} Success response with scheduled count
 */
function scheduleReminders() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAME_REMINDERS);
    
    if (!sheet || sheet.getLastRow() <= 1) {
      return { scheduledCount: 0 };
    }
    
    const data = sheet.getDataRange().getValues();
    const now = new Date();
    let scheduledCount = 0;
    
    // Skip header row
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const [id, name, phone, dateStr, leadDays, sent] = row;
      
      if (sent) continue; // Skip already sent reminders
      
      const eventDate = new Date(dateStr);
      const triggerDate = new Date(eventDate);
      triggerDate.setDate(eventDate.getDate() - leadDays);
      
      if (now >= triggerDate) {
        // Create calendar event
        const calendar = CalendarApp.getDefaultCalendar();
        const eventTitle = `Reminder: Plan gift for ${name}`;
        const eventDescription = `Don't forget to plan a gift for ${name}!\nEvent Date: ${eventDate.toDateString()}\nPhone: ${phone}`;
        
        calendar.createAllDayEvent(eventTitle, eventDate, {
          description: eventDescription
        });
        
        // Mark as sent
        sheet.getRange(i + 1, 6).setValue(true);
        scheduledCount++;
        
        Logger.log(`Scheduled reminder for ${name} on ${eventDate.toDateString()}`);
      }
    }
    
    return { scheduledCount: scheduledCount };
    
  } catch (error) {
    Logger.log('Error in scheduleReminders: ' + error.toString());
    return {
      error: {
        code: 'SCHEDULE_ERROR',
        message: 'Failed to schedule reminders: ' + error.message
      }
    };
  }
}

/**
 * Get checklist items for a specific event
 * @param {string} eventId - The event ID to get checklists for
 * @returns {Array|Object} Array of checklist items or error
 */
function getChecklists(eventId) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(SHEET_NAME_CHECKLISTS);
    
    if (!sheet || sheet.getLastRow() <= 1) {
      return [];
    }
    
    const data = sheet.getDataRange().getValues();
    const headers = data[0];
    const checklists = [];
    
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      if (row[1] === eventId) { // event_id column
        const checklist = {};
        headers.forEach((header, index) => {
          checklist[header] = row[index];
        });
        checklists.push(checklist);
      }
    }
    
    return checklists;
    
  } catch (error) {
    Logger.log('Error in getChecklists: ' + error.toString());
    return {
      error: {
        code: 'CHECKLIST_ERROR',
        message: 'Failed to fetch checklists: ' + error.message
      }
    };
  }
}

/**
 * Helper function to get or create a sheet
 * @param {string} sheetName - Name of the sheet
 * @returns {Sheet} The sheet object
 */
function getOrCreateSheet(sheetName) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  let sheet = spreadsheet.getSheetByName(sheetName);
  
  if (!sheet) {
    sheet = spreadsheet.insertSheet(sheetName);
  }
  
  return sheet;
}

/**
 * Validate contact data structure
 * @param {Object} contact - Contact object to validate
 * @returns {boolean} True if valid, false otherwise
 */
function validateContactData(contact) {
  return contact &&
         typeof contact.contact_name === 'string' &&
         typeof contact.contact_phone === 'string' &&
         typeof contact.event_date === 'string' &&
         typeof contact.lead_time_days === 'number' &&
         contact.contact_name.length > 0 &&
         contact.event_date.length > 0;
}

/**
 * Setup function to initialize sheets with proper headers
 * Run this once to set up the spreadsheet structure
 */
function setupSheets() {
  // Setup Reminders sheet
  const remindersSheet = getOrCreateSheet(SHEET_NAME_REMINDERS);
  const remindersHeaders = ['id', 'contact_name', 'contact_phone', 'event_date', 'lead_time_days', 'reminder_sent'];
  
  if (remindersSheet.getLastRow() === 0) {
    remindersSheet.getRange(1, 1, 1, remindersHeaders.length).setValues([remindersHeaders]);
  }
  
  // Setup Checklists sheet
  const checklistsSheet = getOrCreateSheet(SHEET_NAME_CHECKLISTS);
  const checklistsHeaders = ['list_id', 'event_id', 'item', 'status'];
  
  if (checklistsSheet.getLastRow() === 0) {
    checklistsSheet.getRange(1, 1, 1, checklistsHeaders.length).setValues([checklistsHeaders]);
  }
  
  Logger.log('Sheets setup completed successfully');
}
