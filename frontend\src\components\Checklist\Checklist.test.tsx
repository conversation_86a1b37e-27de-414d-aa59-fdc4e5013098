/**
 * Checklist Component Tests
 * Comprehensive test suite for Checklist component functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Checklist } from './Checklist';
import { IChecklistItem } from '../../types/api';
import { CHECKLIST_MESSAGES } from './types';
import * as apiService from '../../services/api';

// Mock the API service
jest.mock('../../services/api', () => ({
  getChecklists: jest.fn(),
}));

const mockGetChecklists = apiService.getChecklists as jest.MockedFunction<typeof apiService.getChecklists>;

// Sample test data
const mockChecklistItems: IChecklistItem[] = [
  {
    list_id: '1',
    event_id: 'event-1',
    item: 'Buy birthday gift',
    status: 'pending',
  },
  {
    list_id: '2',
    event_id: 'event-1',
    item: 'Wrap the gift',
    status: 'pending',
  },
  {
    list_id: '3',
    event_id: 'event-1',
    item: 'Write birthday card',
    status: 'done',
  },
];

// Mock handlers
const mockHandlers = {
  onItemsChange: jest.fn(),
  onItemAdd: jest.fn(),
  onItemUpdate: jest.fn(),
  onItemDelete: jest.fn(),
};

describe('Checklist Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetChecklists.mockResolvedValue(mockChecklistItems);
  });

  // ============================================================================
  // BASIC RENDERING TESTS
  // ============================================================================

  describe('Basic Rendering', () => {
    it('renders checklist with title', async () => {
      render(<Checklist eventId="event-1" />);
      
      expect(screen.getByText('Gift Planning Checklist')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });
    });

    it('loads checklist items on mount', async () => {
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(mockGetChecklists).toHaveBeenCalledWith('event-1');
      });
    });

    it('displays correct item count and progress', async () => {
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText('1 of 3 completed (33%)')).toBeInTheDocument();
      });
    });

    it('applies correct CSS classes based on props', () => {
      const { rerender } = render(
        <Checklist 
          eventId="event-1" 
          className="custom-class"
          isLoading={true}
        />
      );
      
      const checklist = screen.getByRole('region');
      expect(checklist).toHaveClass('checklist', 'custom-class', 'checklist--loading');
      
      rerender(<Checklist eventId="event-1" error="Test error" />);
      expect(checklist).toHaveClass('checklist--error');
    });
  });

  // ============================================================================
  // ITEM DISPLAY TESTS
  // ============================================================================

  describe('Item Display', () => {
    it('renders all checklist items', async () => {
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
        expect(screen.getByText('Wrap the gift')).toBeInTheDocument();
        expect(screen.getByText('Write birthday card')).toBeInTheDocument();
      });
    });

    it('shows correct checkbox states', async () => {
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        const checkboxes = screen.getAllByRole('checkbox');
        expect(checkboxes).toHaveLength(3);
        
        // First two items should be unchecked (pending)
        expect(checkboxes[0]).not.toBeChecked();
        expect(checkboxes[1]).not.toBeChecked();
        
        // Third item should be checked (done)
        expect(checkboxes[2]).toBeChecked();
      });
    });

    it('applies correct styling to completed items', async () => {
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        const completedItem = screen.getByText('Write birthday card').closest('.checklist__item');
        expect(completedItem).toHaveClass('checklist__item--completed');
      });
    });

    it('shows item action buttons', async () => {
      render(<Checklist eventId="event-1" allowInlineEdit={true} allowDelete={true} />);
      
      await waitFor(() => {
        // Should have toggle, edit, and delete buttons for each item
        expect(screen.getAllByLabelText(/Edit/)).toHaveLength(3);
        expect(screen.getAllByLabelText(/Delete/)).toHaveLength(3);
      });
    });
  });

  // ============================================================================
  // FILTERING TESTS
  // ============================================================================

  describe('Filtering', () => {
    it('filters items by status', async () => {
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Filter to show only pending items
      const filterSelect = screen.getByLabelText('Filter checklist items');
      await userEvent.selectOptions(filterSelect, 'pending');
      
      expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      expect(screen.getByText('Wrap the gift')).toBeInTheDocument();
      expect(screen.queryByText('Write birthday card')).not.toBeInTheDocument();
    });

    it('shows correct counts in filter options', async () => {
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        const filterSelect = screen.getByLabelText('Filter checklist items');
        expect(filterSelect).toHaveTextContent('All Items (3)');
        expect(filterSelect).toHaveTextContent('Pending (2)');
        expect(filterSelect).toHaveTextContent('Completed (1)');
      });
    });

    it('hides completed filter when showCompleted is false', async () => {
      render(<Checklist eventId="event-1" showCompleted={false} />);
      
      await waitFor(() => {
        const filterSelect = screen.getByLabelText('Filter checklist items');
        expect(filterSelect).not.toHaveTextContent('Completed');
      });
    });
  });

  // ============================================================================
  // ADD ITEM TESTS
  // ============================================================================

  describe('Add Item Functionality', () => {
    it('shows add form when add button is clicked', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      const addButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addButton);
      
      expect(screen.getByPlaceholderText('Enter gift planning item...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /add item/i })).toHaveTextContent('Cancel');
    });

    it('adds new item when form is submitted', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" {...mockHandlers} />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Open add form
      const addButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addButton);
      
      // Type new item
      const input = screen.getByPlaceholderText('Enter gift planning item...');
      await user.type(input, 'Buy flowers');
      
      // Submit form
      const submitButton = screen.getByRole('button', { name: 'Add Item' });
      await user.click(submitButton);
      
      // Check that item appears in the list
      await waitFor(() => {
        expect(screen.getByText('Buy flowers')).toBeInTheDocument();
      });
      
      // Check that callback was called
      expect(mockHandlers.onItemAdd).toHaveBeenCalled();
    });

    it('validates empty item text', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Open add form
      const addButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addButton);
      
      // Try to submit empty form
      const submitButton = screen.getByRole('button', { name: 'Add Item' });
      await user.click(submitButton);
      
      expect(screen.getByText(CHECKLIST_MESSAGES.ERROR_EMPTY_ITEM)).toBeInTheDocument();
    });

    it('prevents duplicate items', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Open add form
      const addButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addButton);
      
      // Type existing item
      const input = screen.getByPlaceholderText('Enter gift planning item...');
      await user.type(input, 'Buy birthday gift');
      
      // Submit form
      const submitButton = screen.getByRole('button', { name: 'Add Item' });
      await user.click(submitButton);
      
      expect(screen.getByText(CHECKLIST_MESSAGES.ERROR_DUPLICATE_ITEM)).toBeInTheDocument();
    });

    it('supports keyboard shortcuts in add form', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" {...mockHandlers} />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Open add form
      const addButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addButton);
      
      const input = screen.getByPlaceholderText('Enter gift planning item...');
      await user.type(input, 'Test item');
      
      // Submit with Enter key
      await user.keyboard('{Enter}');
      
      await waitFor(() => {
        expect(screen.getByText('Test item')).toBeInTheDocument();
      });
    });

    it('cancels add form with Escape key', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Open add form
      const addButton = screen.getByRole('button', { name: /add item/i });
      await user.click(addButton);
      
      const input = screen.getByPlaceholderText('Enter gift planning item...');
      await user.type(input, 'Test item');
      
      // Cancel with Escape key
      await user.keyboard('{Escape}');
      
      expect(screen.queryByPlaceholderText('Enter gift planning item...')).not.toBeInTheDocument();
    });

    it('respects maxItems limit', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" maxItems={3} />);
      
      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Should not show add button when at max items
      expect(screen.queryByRole('button', { name: /add item/i })).not.toBeInTheDocument();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility', () => {
    it('has proper ARIA attributes', async () => {
      render(<Checklist eventId="event-1" />);

      const checklist = screen.getByRole('region');
      expect(checklist).toHaveAttribute('aria-label', 'Gift planning checklist');

      await waitFor(() => {
        const itemsList = screen.getByRole('list');
        expect(itemsList).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<Checklist eventId="event-1" allowInlineEdit={true} />);

      await waitFor(() => {
        expect(screen.getByText('Buy birthday gift')).toBeInTheDocument();
      });

      // Tab through interactive elements
      await user.tab();
      expect(screen.getByLabelText('Filter checklist items')).toHaveFocus();

      await user.tab();
      expect(screen.getByRole('button', { name: /add item/i })).toHaveFocus();
    });

    it('announces errors to screen readers', async () => {
      render(<Checklist eventId="event-1" error="Test error" />);

      const errorElement = screen.getByRole('alert');
      expect(errorElement).toBeInTheDocument();
      expect(errorElement).toHaveTextContent('Test error');
    });
  });
});
