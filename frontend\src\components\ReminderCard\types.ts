/**
 * TypeScript interfaces for ReminderCard component
 * Defines props, state, and data structures for reminder card functionality
 */

import { IContact } from '../../types/api';

// ============================================================================
// COMPONENT PROPS INTERFACES
// ============================================================================

export interface IReminderCardProps {
  /** The reminder/event data to display */
  event: IContact;
  
  /** Optional callback when reminder is marked as sent */
  onReminderSent?: (eventId: string) => void;
  
  /** Optional callback when reminder is snoozed */
  onSnooze?: (eventId: string, snoozeHours: number) => void;
  
  /** Optional callback when event is edited */
  onEdit?: (event: IContact) => void;
  
  /** Optional callback when event is deleted */
  onDelete?: (eventId: string) => void;
  
  /** Optional callback when checklist is requested */
  onShowChecklist?: (eventId: string) => void;
  
  /** Whether the card is in a loading state */
  isLoading?: boolean;
  
  /** Optional error message to display */
  error?: string;
  
  /** Optional CSS class name for styling */
  className?: string;
  
  /** Whether to show action buttons */
  showActions?: boolean;
  
  /** Whether to show the checklist button */
  showChecklistButton?: boolean;
  
  /** Whether the card is selected/highlighted */
  isSelected?: boolean;
  
  /** Whether to show detailed information */
  showDetails?: boolean;
}

// ============================================================================
// COMPONENT STATE INTERFACES
// ============================================================================

export interface IReminderCardState {
  /** Whether the card is expanded to show details */
  isExpanded: boolean;
  
  /** Whether an action is currently being processed */
  isProcessingAction: boolean;
  
  /** Current error message, if any */
  error: string | null;
  
  /** Whether the snooze dropdown is open */
  isSnoozeDropdownOpen: boolean;
  
  /** Selected snooze duration in hours */
  selectedSnoozeDuration: number;
}

// ============================================================================
// DISPLAY DATA INTERFACES
// ============================================================================

export interface IReminderDisplayData extends IContact {
  /** Calculated days until event */
  daysUntilEvent: number;
  
  /** Calculated reminder date */
  reminderDate: string;
  
  /** Whether the reminder is overdue */
  isOverdue: boolean;
  
  /** Whether the reminder should be sent today */
  isDueToday: boolean;
  
  /** Formatted display date */
  displayDate: string;
  
  /** Formatted reminder date for display */
  displayReminderDate: string;
  
  /** Status display text */
  statusText: string;
  
  /** Status color/theme */
  statusTheme: 'pending' | 'due-today' | 'overdue' | 'sent';
  
  /** Countdown text (e.g., "In 5 days", "Today", "2 days ago") */
  countdownText: string;
  
  /** Whether the event has passed */
  isEventPassed: boolean;
}

// ============================================================================
// ACTION INTERFACES
// ============================================================================

export interface ISnoozeOption {
  label: string;
  hours: number;
  description?: string;
}

export interface ICardAction {
  id: string;
  label: string;
  icon?: string;
  variant: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  onClick: () => void;
}

// ============================================================================
// CONSTANTS AND DEFAULTS
// ============================================================================

export const DEFAULT_REMINDER_CARD_STATE: IReminderCardState = {
  isExpanded: false,
  isProcessingAction: false,
  error: null,
  isSnoozeDropdownOpen: false,
  selectedSnoozeDuration: 24, // Default to 24 hours
};

export const SNOOZE_OPTIONS: ISnoozeOption[] = [
  { label: '1 Hour', hours: 1, description: 'Remind me in 1 hour' },
  { label: '4 Hours', hours: 4, description: 'Remind me in 4 hours' },
  { label: '1 Day', hours: 24, description: 'Remind me tomorrow' },
  { label: '3 Days', hours: 72, description: 'Remind me in 3 days' },
  { label: '1 Week', hours: 168, description: 'Remind me next week' },
];

export const REMINDER_CARD_MESSAGES = {
  LOADING: 'Loading reminder details...',
  ERROR_GENERIC: 'An error occurred while processing the reminder',
  ERROR_NETWORK: 'Network error. Please check your connection and try again.',
  ERROR_REMINDER_SENT: 'Failed to mark reminder as sent',
  ERROR_SNOOZE: 'Failed to snooze reminder',
  ERROR_DELETE: 'Failed to delete reminder',
  SUCCESS_REMINDER_SENT: 'Reminder marked as sent successfully',
  SUCCESS_SNOOZE: 'Reminder snoozed successfully',
  SUCCESS_DELETE: 'Reminder deleted successfully',
  CONFIRM_DELETE: 'Are you sure you want to delete this reminder? This action cannot be undone.',
  ARIA_EXPAND: 'Expand reminder details',
  ARIA_COLLAPSE: 'Collapse reminder details',
  ARIA_MARK_SENT: 'Mark reminder as sent',
  ARIA_SNOOZE: 'Snooze reminder',
  ARIA_EDIT: 'Edit reminder',
  ARIA_DELETE: 'Delete reminder',
  ARIA_CHECKLIST: 'View gift planning checklist',
} as const;

// ============================================================================
// VALIDATION AND HELPER TYPES
// ============================================================================

export type ReminderCardError = 
  | 'REMINDER_SENT_ERROR'
  | 'SNOOZE_ERROR'
  | 'DELETE_ERROR'
  | 'NETWORK_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

export type ReminderCardAction = 
  | 'mark_sent'
  | 'snooze'
  | 'edit'
  | 'delete'
  | 'show_checklist';

// ============================================================================
// UTILITY TYPE GUARDS
// ============================================================================

export const isValidSnoozeHours = (hours: number): boolean => {
  return Number.isInteger(hours) && hours > 0 && hours <= 8760; // Max 1 year
};

export const isValidEventId = (eventId: string): boolean => {
  return typeof eventId === 'string' && eventId.trim().length > 0;
};
