/**
 * ImportForm Component
 * Handles contact import with form validation, error handling, and accessibility
 * 
 * Features:
 * - Real-time form validation
 * - TypeScript interfaces for type safety
 * - Comprehensive error handling
 * - Accessibility compliance (ARIA labels, keyboard navigation)
 * - Loading states and user feedback
 * - Responsive design considerations
 */

import React, { useState, useCallback, useEffect } from 'react';
import { format } from 'date-fns';
import {
  IImportFormProps,
  IFormState,
  IFormErrors,
  DEFAULT_FORM_DATA,
  DEFAULT_FORM_ERRORS,
  DEFAULT_TOUCHED,
  VALIDATION_RULES,
} from './types';
import './ImportForm.css';

export const ImportForm: React.FC<IImportFormProps> = ({
  onSubmit,
  onSuccess,
  onError,
  isLoading = false,
  initialData = {},
  className = '',
  showSuccessMessage = false,
  resetOnSuccess = false,
}) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [formState, setFormState] = useState<IFormState>({
    data: { ...DEFAULT_FORM_DATA, ...initialData },
    errors: DEFAULT_FORM_ERRORS,
    isSubmitting: false,
    isValid: false,
    touched: DEFAULT_TOUCHED,
  });

  const [successMessage, setSuccessMessage] = useState<string>('');

  // ============================================================================
  // VALIDATION FUNCTIONS
  // ============================================================================

  const validateField = useCallback((name: keyof typeof VALIDATION_RULES, value: any): string | undefined => {
    const rules = VALIDATION_RULES[name];
    
    if (rules.required && (!value || value.toString().trim() === '')) {
      return `${name.replace('_', ' ')} is required`.replace(/\b\w/g, l => l.toUpperCase());
    }

    if (value && rules.minLength && value.toString().length < rules.minLength) {
      return `${name.replace('_', ' ')} must be at least ${rules.minLength} characters`.replace(/\b\w/g, l => l.toUpperCase());
    }

    if (value && rules.maxLength && value.toString().length > rules.maxLength) {
      return `${name.replace('_', ' ')} must be no more than ${rules.maxLength} characters`.replace(/\b\w/g, l => l.toUpperCase());
    }

    if (value && rules.pattern && !rules.pattern.test(value.toString())) {
      switch (name) {
        case 'contact_name':
          return 'Please enter a valid name (letters, spaces, hyphens, and apostrophes only)';
        case 'contact_phone':
          return 'Please enter a valid phone number';
        default:
          return 'Invalid format';
      }
    }

    if (value && rules.min && Number(value) < rules.min) {
      return `${name.replace('_', ' ')} must be at least ${rules.min}`.replace(/\b\w/g, l => l.toUpperCase());
    }

    if (value && rules.max && Number(value) > rules.max) {
      return `${name.replace('_', ' ')} must be no more than ${rules.max}`.replace(/\b\w/g, l => l.toUpperCase());
    }

    if (rules.custom) {
      return rules.custom(value);
    }

    return undefined;
  }, []);

  const validateForm = useCallback((data: typeof formState.data): IFormErrors => {
    const errors: IFormErrors = {};

    Object.keys(VALIDATION_RULES).forEach((key) => {
      const fieldName = key as keyof typeof VALIDATION_RULES;
      const error = validateField(fieldName, data[fieldName]);
      if (error) {
        errors[fieldName] = error;
      }
    });

    return errors;
  }, [validateField]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleInputChange = useCallback((name: keyof typeof formState.data, value: string | number) => {
    setFormState(prev => {
      const newData = { ...prev.data, [name]: value };
      const fieldError = validateField(name, value);
      const newErrors = { ...prev.errors };
      
      if (fieldError) {
        newErrors[name] = fieldError;
      } else {
        delete newErrors[name];
      }

      const allErrors = validateForm(newData);
      const isValid = Object.keys(allErrors).length === 0;

      return {
        ...prev,
        data: newData,
        errors: newErrors,
        isValid,
        touched: { ...prev.touched, [name]: true },
      };
    });

    // Clear success message when user starts typing
    if (successMessage) {
      setSuccessMessage('');
    }
  }, [validateField, validateForm, successMessage]);

  const handleBlur = useCallback((name: keyof typeof formState.data) => {
    setFormState(prev => ({
      ...prev,
      touched: { ...prev.touched, [name]: true },
    }));
  }, []);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    setFormState(prev => ({
      ...prev,
      touched: {
        contact_name: true,
        contact_phone: true,
        event_date: true,
        lead_time_days: true,
      },
    }));

    // Validate all fields
    const errors = validateForm(formState.data);
    
    if (Object.keys(errors).length > 0) {
      setFormState(prev => ({ ...prev, errors }));
      return;
    }

    setFormState(prev => ({ ...prev, isSubmitting: true, errors: {} }));

    try {
      await onSubmit(formState.data);
      
      if (showSuccessMessage) {
        setSuccessMessage('Contact imported successfully!');
      }
      
      if (onSuccess) {
        onSuccess(1); // Assuming 1 contact imported
      }

      if (resetOnSuccess) {
        setFormState(prev => ({
          ...prev,
          data: { ...DEFAULT_FORM_DATA, ...initialData },
          touched: DEFAULT_TOUCHED,
          isSubmitting: false,
        }));
      } else {
        setFormState(prev => ({ ...prev, isSubmitting: false }));
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      setFormState(prev => ({
        ...prev,
        errors: { general: errorMessage },
        isSubmitting: false,
      }));

      if (onError) {
        onError(errorMessage);
      }
    }
  }, [formState.data, validateForm, onSubmit, onSuccess, onError, showSuccessMessage, resetOnSuccess, initialData]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    const errors = validateForm(formState.data);
    const isValid = Object.keys(errors).length === 0;
    
    setFormState(prev => ({ ...prev, isValid }));
  }, [formState.data, validateForm]);

  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  const getFieldError = (fieldName: keyof IFormErrors): string | undefined => {
    return formState.touched[fieldName as keyof typeof formState.touched] ? formState.errors[fieldName] : undefined;
  };

  const getFieldId = (fieldName: string): string => `import-form-${fieldName}`;
  const getErrorId = (fieldName: string): string => `import-form-${fieldName}-error`;

  // ============================================================================
  // RENDER
  // ============================================================================

  const isFormDisabled = isLoading || formState.isSubmitting;

  return (
    <form 
      className={`import-form ${className}`}
      onSubmit={handleSubmit}
      noValidate
      aria-label="Import Contact Form"
    >
      <div className="import-form__header">
        <h2 className="import-form__title">Import Contact</h2>
        <p className="import-form__description">
          Add a new contact with reminder details for upcoming events.
        </p>
      </div>

      {/* General Error Message */}
      {formState.errors.general && (
        <div className="import-form__error import-form__error--general" role="alert">
          {formState.errors.general}
        </div>
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="import-form__success" role="alert">
          {successMessage}
        </div>
      )}

      <div className="import-form__fields">
        {/* Contact Name Field */}
        <div className="import-form__field">
          <label 
            htmlFor={getFieldId('contact-name')}
            className="import-form__label"
          >
            Contact Name *
          </label>
          <input
            id={getFieldId('contact-name')}
            type="text"
            className={`import-form__input ${getFieldError('contact_name') ? 'import-form__input--error' : ''}`}
            value={formState.data.contact_name}
            onChange={(e) => handleInputChange('contact_name', e.target.value)}
            onBlur={() => handleBlur('contact_name')}
            disabled={isFormDisabled}
            aria-required="true"
            aria-describedby={getFieldError('contact_name') ? getErrorId('contact-name') : undefined}
            placeholder="Enter contact name"
          />
          {getFieldError('contact_name') && (
            <div 
              id={getErrorId('contact-name')}
              className="import-form__field-error"
              role="alert"
            >
              {getFieldError('contact_name')}
            </div>
          )}
        </div>

        {/* Phone Number Field */}
        <div className="import-form__field">
          <label 
            htmlFor={getFieldId('phone-number')}
            className="import-form__label"
          >
            Phone Number *
          </label>
          <input
            id={getFieldId('phone-number')}
            type="tel"
            className={`import-form__input ${getFieldError('contact_phone') ? 'import-form__input--error' : ''}`}
            value={formState.data.contact_phone}
            onChange={(e) => handleInputChange('contact_phone', e.target.value)}
            onBlur={() => handleBlur('contact_phone')}
            disabled={isFormDisabled}
            aria-required="true"
            aria-describedby={getFieldError('contact_phone') ? getErrorId('phone-number') : undefined}
            placeholder="+1234567890"
          />
          {getFieldError('contact_phone') && (
            <div 
              id={getErrorId('phone-number')}
              className="import-form__field-error"
              role="alert"
            >
              {getFieldError('contact_phone')}
            </div>
          )}
        </div>

        {/* Event Date Field */}
        <div className="import-form__field">
          <label 
            htmlFor={getFieldId('event-date')}
            className="import-form__label"
          >
            Event Date *
          </label>
          <input
            id={getFieldId('event-date')}
            type="date"
            className={`import-form__input ${getFieldError('event_date') ? 'import-form__input--error' : ''}`}
            value={formState.data.event_date}
            onChange={(e) => handleInputChange('event_date', e.target.value)}
            onBlur={() => handleBlur('event_date')}
            disabled={isFormDisabled}
            aria-required="true"
            aria-describedby={getFieldError('event_date') ? getErrorId('event-date') : undefined}
            min={format(new Date(), 'yyyy-MM-dd')}
          />
          {getFieldError('event_date') && (
            <div 
              id={getErrorId('event-date')}
              className="import-form__field-error"
              role="alert"
            >
              {getFieldError('event_date')}
            </div>
          )}
        </div>

        {/* Lead Time Field */}
        <div className="import-form__field">
          <label 
            htmlFor={getFieldId('lead-time')}
            className="import-form__label"
          >
            Lead Time (Days) *
          </label>
          <input
            id={getFieldId('lead-time')}
            type="number"
            className={`import-form__input ${getFieldError('lead_time_days') ? 'import-form__input--error' : ''}`}
            value={formState.data.lead_time_days}
            onChange={(e) => handleInputChange('lead_time_days', parseInt(e.target.value) || 0)}
            onBlur={() => handleBlur('lead_time_days')}
            disabled={isFormDisabled}
            aria-required="true"
            aria-describedby={getFieldError('lead_time_days') ? getErrorId('lead-time') : undefined}
            min="1"
            max="365"
            placeholder="7"
          />
          {getFieldError('lead_time_days') && (
            <div 
              id={getErrorId('lead-time')}
              className="import-form__field-error"
              role="alert"
            >
              {getFieldError('lead_time_days')}
            </div>
          )}
          <div className="import-form__field-help">
            Number of days before the event to send reminder
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="import-form__actions">
        <button
          type="submit"
          className="import-form__submit"
          disabled={isFormDisabled || !formState.isValid}
          aria-describedby={formState.errors.general ? 'import-form-general-error' : undefined}
        >
          {isFormDisabled ? 'Importing...' : 'Import Contact'}
        </button>
      </div>
    </form>
  );
};
