/**
 * Unit tests for API service layer
 * Tests all API functions, error handling, and retry logic
 */

import { ApiClient } from './api';
import { IContactData } from '../types/api';

// Mock fetch globally
global.fetch = jest.fn();

describe('ApiClient', () => {
  let apiClient: ApiClient;
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    apiClient = new ApiClient({ baseUrl: 'https://test-api.com' });
    mockFetch.mockClear();
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  describe('healthCheck', () => {
    it('should return success message on successful health check', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => 'Reminder API is running - OK',
      } as Response);

      const result = await apiClient.healthCheck();
      expect(result).toBe('Reminder API is running - OK');
      expect(mockFetch).toHaveBeenCalledWith('https://test-api.com/', {
        method: 'GET',
        signal: expect.any(AbortSignal),
        headers: { 'Content-Type': 'application/json' },
      });
    });

    it('should throw error on failed health check', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(apiClient.healthCheck()).rejects.toThrow(
        'Health check failed: Network error: Network error'
      );
    });
  });

  describe('importContacts', () => {
    const validContactData: IContactData[] = [
      {
        contact_name: 'John Doe',
        contact_phone: '+**********',
        event_date: '2025-12-25',
        lead_time_days: 7,
      },
    ];

    it('should successfully import contacts', async () => {
      const mockResponse = { success: true, importedCount: 1 };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await apiClient.importContacts(validContactData);
      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://test-api.com/?action=importContacts',
        {
          method: 'POST',
          body: JSON.stringify({ data: validContactData }),
          signal: expect.any(AbortSignal),
          headers: { 'Content-Type': 'application/json' },
        }
      );
    });

    it('should validate input data - empty array', async () => {
      await expect(apiClient.importContacts([])).rejects.toThrow(
        'Contact data must be a non-empty array'
      );
    });

    it('should validate input data - missing contact name', async () => {
      const invalidData = [{ ...validContactData[0], contact_name: '' }];
      await expect(apiClient.importContacts(invalidData)).rejects.toThrow(
        'Contact name is required and must be a string'
      );
    });

    it('should validate input data - invalid lead time', async () => {
      const invalidData = [{ ...validContactData[0], lead_time_days: -1 }];
      await expect(apiClient.importContacts(invalidData)).rejects.toThrow(
        'Lead time days must be a non-negative number'
      );
    });

    it('should handle API error response', async () => {
      const errorResponse = {
        error: { code: 'IMPORT_ERROR', message: 'Import failed' },
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => errorResponse,
      } as Response);

      await expect(apiClient.importContacts(validContactData)).rejects.toThrow(
        'Import failed'
      );
    });
  });

  describe('getEvents', () => {
    it('should successfully fetch events', async () => {
      const mockEvents = [
        {
          id: '1',
          contact_name: 'John Doe',
          contact_phone: '+**********',
          event_date: '2025-12-25',
          lead_time_days: 7,
          reminder_sent: false,
        },
      ];
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockEvents,
      } as Response);

      const result = await apiClient.getEvents();
      expect(result).toEqual(mockEvents);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://test-api.com/?action=getEvents',
        {
          method: 'GET',
          signal: expect.any(AbortSignal),
          headers: { 'Content-Type': 'application/json' },
        }
      );
    });

    it('should handle API error response', async () => {
      const errorResponse = {
        error: { code: 'FETCH_ERROR', message: 'Failed to fetch' },
      };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => errorResponse,
      } as Response);

      await expect(apiClient.getEvents()).rejects.toThrow('Failed to fetch');
    });
  });

  describe('scheduleReminders', () => {
    it('should successfully schedule reminders', async () => {
      const mockResponse = { scheduledCount: 3 };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      } as Response);

      const result = await apiClient.scheduleReminders();
      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://test-api.com/?action=scheduleReminders',
        {
          method: 'POST',
          body: JSON.stringify({}),
          signal: expect.any(AbortSignal),
          headers: { 'Content-Type': 'application/json' },
        }
      );
    });
  });

  describe('getChecklists', () => {
    it('should successfully fetch checklists', async () => {
      const mockChecklists = [
        {
          list_id: '1',
          event_id: 'event-1',
          item: 'Buy gift',
          status: 'pending' as const,
        },
      ];
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockChecklists,
      } as Response);

      const result = await apiClient.getChecklists('event-1');
      expect(result).toEqual(mockChecklists);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://test-api.com/?action=getChecklists&eventId=event-1',
        {
          method: 'GET',
          signal: expect.any(AbortSignal),
          headers: { 'Content-Type': 'application/json' },
        }
      );
    });

    it('should validate eventId parameter', async () => {
      await expect(apiClient.getChecklists('')).rejects.toThrow(
        'Event ID is required and must be a string'
      );
    });
  });

  describe('retry logic', () => {
    it('should retry failed requests', async () => {
      const apiClientWithRetry = new ApiClient({
        baseUrl: 'https://test-api.com',
        retryAttempts: 2,
        retryDelay: 100,
      });

      // First call fails, second succeeds
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: async () => 'OK',
        } as Response);

      const result = await apiClientWithRetry.healthCheck();
      expect(result).toBe('OK');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retry attempts', async () => {
      const apiClientWithRetry = new ApiClient({
        baseUrl: 'https://test-api.com',
        retryAttempts: 1,
        retryDelay: 100,
      });

      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(apiClientWithRetry.healthCheck()).rejects.toThrow(
        'Network error: Network error'
      );
      expect(mockFetch).toHaveBeenCalledTimes(2); // Initial + 1 retry
    });
  });

  describe('timeout handling', () => {
    it('should handle request timeout', async () => {
      const apiClientWithTimeout = new ApiClient({
        baseUrl: 'https://test-api.com',
        timeout: 100,
        retryAttempts: 0,
      });

      // Mock a request that never resolves
      mockFetch.mockImplementationOnce(() => new Promise(() => {}));

      await expect(apiClientWithTimeout.healthCheck()).rejects.toThrow(
        'Request timed out'
      );
    });
  });
});
