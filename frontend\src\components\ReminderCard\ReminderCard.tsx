/**
 * ReminderCard Component
 * Display individual reminder details with countdown, action buttons, and responsive design
 * 
 * Features:
 * - Real-time countdown display
 * - Action buttons (<PERSON>, Snooze, Edit, Delete)
 * - Expandable details view
 * - TypeScript interfaces for type safety
 * - Comprehensive error handling
 * - Accessibility compliance (ARIA labels, keyboard navigation)
 * - Responsive design considerations
 * - Status-based visual theming
 */

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { format, parseISO, differenceInDays, addDays, isPast } from 'date-fns';
import {
  IReminderCardProps,
  IReminderCardState,
  IReminderDisplayData,
  ISnoozeOption,
  ICardAction,
  DEFAULT_REMINDER_CARD_STATE,
  SNOOZE_OPTIONS,
  REMINDER_CARD_MESSAGES,
  ReminderCardAction,
  isValidSnoozeHours,
  isValidEventId,
} from './types';
import './ReminderCard.css';

export const ReminderCard: React.FC<IReminderCardProps> = ({
  event,
  onReminderSent,
  onSnooze,
  onEdit,
  onDelete,
  onShowChecklist,
  isLoading: externalLoading = false,
  error: externalError,
  className = '',
  showActions = true,
  showChecklistButton = true,
  isSelected = false,
  showDetails = false,
}) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  const [state, setState] = useState<IReminderCardState>({
    ...DEFAULT_REMINDER_CARD_STATE,
    isExpanded: showDetails,
  });

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const isLoading = externalLoading || state.isProcessingAction;
  const error = externalError || state.error;

  // Transform event to display data with calculated fields
  const displayData = useMemo((): IReminderDisplayData => {
    const eventDate = parseISO(event.event_date);
    const today = new Date();
    const daysUntilEvent = differenceInDays(eventDate, today);
    const reminderDate = format(
      addDays(eventDate, -event.lead_time_days),
      'yyyy-MM-dd',
    );
    const reminderDateObj = parseISO(reminderDate);
    const daysUntilReminder = differenceInDays(reminderDateObj, today);
    const isEventPassed = isPast(eventDate);

    // Determine status theme
    let statusTheme: IReminderDisplayData['statusTheme'];
    let statusText: string;
    let countdownText: string;

    if (event.reminder_sent) {
      statusTheme = 'sent';
      statusText = 'Reminder Sent';
      countdownText = isEventPassed 
        ? `Event was ${Math.abs(daysUntilEvent)} days ago`
        : daysUntilEvent === 0 
        ? 'Event is today'
        : `Event in ${daysUntilEvent} days`;
    } else if (daysUntilReminder < 0) {
      statusTheme = 'overdue';
      statusText = 'Overdue';
      countdownText = `${Math.abs(daysUntilReminder)} days overdue`;
    } else if (daysUntilReminder === 0) {
      statusTheme = 'due-today';
      statusText = 'Due Today';
      countdownText = 'Send reminder today';
    } else {
      statusTheme = 'pending';
      statusText = `Due in ${daysUntilReminder} days`;
      countdownText = `Reminder in ${daysUntilReminder} days`;
    }

    return {
      ...event,
      daysUntilEvent,
      reminderDate,
      isOverdue: daysUntilReminder < 0 && !event.reminder_sent,
      isDueToday: daysUntilReminder === 0 && !event.reminder_sent,
      displayDate: format(eventDate, 'MMM dd, yyyy'),
      displayReminderDate: format(reminderDateObj, 'MMM dd, yyyy'),
      statusText,
      statusTheme,
      countdownText,
      isEventPassed,
    };
  }, [event]);

  // Generate action buttons based on props and state
  const actions = useMemo((): ICardAction[] => {
    if (!showActions) return [];

    const actionList: ICardAction[] = [];

    // Mark as sent action (only if not already sent)
    if (!event.reminder_sent) {
      actionList.push({
        id: 'mark_sent',
        label: 'Mark Sent',
        icon: '✓',
        variant: 'primary',
        disabled: isLoading,
        onClick: () => handleAction('mark_sent'),
      });

      // Snooze action (only if not already sent)
      actionList.push({
        id: 'snooze',
        label: 'Snooze',
        icon: '⏰',
        variant: 'secondary',
        disabled: isLoading,
        onClick: () => handleAction('snooze'),
      });
    }

    // Edit action
    if (onEdit) {
      actionList.push({
        id: 'edit',
        label: 'Edit',
        icon: '✏️',
        variant: 'secondary',
        disabled: isLoading,
        onClick: () => handleAction('edit'),
      });
    }

    // Checklist action
    if (showChecklistButton && onShowChecklist) {
      actionList.push({
        id: 'show_checklist',
        label: 'Checklist',
        icon: '📋',
        variant: 'secondary',
        disabled: isLoading,
        onClick: () => handleAction('show_checklist'),
      });
    }

    // Delete action
    if (onDelete) {
      actionList.push({
        id: 'delete',
        label: 'Delete',
        icon: '🗑️',
        variant: 'danger',
        disabled: isLoading,
        onClick: () => handleAction('delete'),
      });
    }

    return actionList;
  }, [showActions, event.reminder_sent, isLoading, onEdit, onDelete, onShowChecklist, showChecklistButton]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleAction = useCallback(async (action: ReminderCardAction) => {
    if (!isValidEventId(event.id)) {
      setState(prev => ({ ...prev, error: 'Invalid event ID' }));
      return;
    }

    setState(prev => ({ ...prev, isProcessingAction: true, error: null }));

    try {
      switch (action) {
        case 'mark_sent':
          if (onReminderSent) {
            await onReminderSent(event.id);
          }
          break;

        case 'snooze':
          setState(prev => ({ 
            ...prev, 
            isSnoozeDropdownOpen: !prev.isSnoozeDropdownOpen,
            isProcessingAction: false,
          }));
          return;

        case 'edit':
          if (onEdit) {
            onEdit(event);
          }
          break;

        case 'delete':
          if (window.confirm(REMINDER_CARD_MESSAGES.CONFIRM_DELETE)) {
            if (onDelete) {
              await onDelete(event.id);
            }
          }
          break;

        case 'show_checklist':
          if (onShowChecklist) {
            onShowChecklist(event.id);
          }
          break;

        default:
          throw new Error(`Unknown action: ${action}`);
      }

      setState(prev => ({ ...prev, isProcessingAction: false }));
    } catch (error) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : REMINDER_CARD_MESSAGES.ERROR_GENERIC;
      
      setState(prev => ({
        ...prev,
        isProcessingAction: false,
        error: errorMessage,
      }));
    }
  }, [event, onReminderSent, onSnooze, onEdit, onDelete, onShowChecklist]);

  const handleSnooze = useCallback(async (hours: number) => {
    if (!isValidSnoozeHours(hours)) {
      setState(prev => ({ ...prev, error: 'Invalid snooze duration' }));
      return;
    }

    setState(prev => ({ 
      ...prev, 
      isProcessingAction: true, 
      isSnoozeDropdownOpen: false,
      error: null,
    }));

    try {
      if (onSnooze) {
        await onSnooze(event.id, hours);
      }
      setState(prev => ({ ...prev, isProcessingAction: false }));
    } catch (error) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : REMINDER_CARD_MESSAGES.ERROR_SNOOZE;
      
      setState(prev => ({
        ...prev,
        isProcessingAction: false,
        error: errorMessage,
      }));
    }
  }, [event.id, onSnooze]);

  const toggleExpanded = useCallback(() => {
    setState(prev => ({ ...prev, isExpanded: !prev.isExpanded }));
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const closeSnoozeDropdown = useCallback(() => {
    setState(prev => ({ ...prev, isSnoozeDropdownOpen: false }));
  }, []);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    setState(prev => ({ ...prev, isExpanded: showDetails }));
  }, [showDetails]);

  // Close snooze dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (state.isSnoozeDropdownOpen) {
        const target = event.target as Element;
        if (!target.closest('.reminder-card__snooze-dropdown')) {
          closeSnoozeDropdown();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [state.isSnoozeDropdownOpen, closeSnoozeDropdown]);

  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  const getCardClassName = (): string => {
    const baseClass = 'reminder-card';
    const classes = [baseClass];
    
    if (className) classes.push(className);
    if (isSelected) classes.push(`${baseClass}--selected`);
    if (isLoading) classes.push(`${baseClass}--loading`);
    if (error) classes.push(`${baseClass}--error`);
    if (state.isExpanded) classes.push(`${baseClass}--expanded`);
    
    classes.push(`${baseClass}--${displayData.statusTheme}`);
    
    return classes.join(' ');
  };

  const getStatusClassName = (): string => {
    return `reminder-card__status reminder-card__status--${displayData.statusTheme}`;
  };

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const renderHeader = () => (
    <div className="reminder-card__header">
      <div className="reminder-card__contact-info">
        <h3 className="reminder-card__contact-name">{event.contact_name}</h3>
        <p className="reminder-card__contact-phone">{event.contact_phone}</p>
      </div>
      <div className="reminder-card__event-info">
        <div className="reminder-card__event-date">{displayData.displayDate}</div>
        <div className="reminder-card__countdown">{displayData.countdownText}</div>
      </div>
      <div className={getStatusClassName()}>
        {displayData.statusText}
      </div>
    </div>
  );

  const renderDetails = () => {
    if (!state.isExpanded) return null;

    return (
      <div className="reminder-card__details">
        <div className="reminder-card__detail-row">
          <span className="reminder-card__detail-label">Reminder Date:</span>
          <span className="reminder-card__detail-value">{displayData.displayReminderDate}</span>
        </div>
        <div className="reminder-card__detail-row">
          <span className="reminder-card__detail-label">Lead Time:</span>
          <span className="reminder-card__detail-value">{event.lead_time_days} days</span>
        </div>
        <div className="reminder-card__detail-row">
          <span className="reminder-card__detail-label">Days Until Event:</span>
          <span className="reminder-card__detail-value">
            {displayData.daysUntilEvent >= 0
              ? `${displayData.daysUntilEvent} days`
              : `${Math.abs(displayData.daysUntilEvent)} days ago`
            }
          </span>
        </div>
        {displayData.isEventPassed && (
          <div className="reminder-card__detail-row">
            <span className="reminder-card__detail-label">Status:</span>
            <span className="reminder-card__detail-value reminder-card__detail-value--warning">
              Event has passed
            </span>
          </div>
        )}
      </div>
    );
  };

  const renderSnoozeDropdown = () => {
    if (!state.isSnoozeDropdownOpen) return null;

    return (
      <div className="reminder-card__snooze-dropdown">
        <div className="reminder-card__snooze-header">
          <span>Snooze for:</span>
          <button
            type="button"
            onClick={closeSnoozeDropdown}
            className="reminder-card__snooze-close"
            aria-label="Close snooze options"
          >
            ✕
          </button>
        </div>
        <div className="reminder-card__snooze-options">
          {SNOOZE_OPTIONS.map((option: ISnoozeOption) => (
            <button
              key={option.hours}
              type="button"
              onClick={() => handleSnooze(option.hours)}
              className="reminder-card__snooze-option"
              disabled={isLoading}
            >
              <span className="reminder-card__snooze-option-label">{option.label}</span>
              {option.description && (
                <span className="reminder-card__snooze-option-description">
                  {option.description}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>
    );
  };

  const renderActions = () => {
    if (!showActions || actions.length === 0) return null;

    return (
      <div className="reminder-card__actions">
        {actions.map((action: ICardAction) => (
          <button
            key={action.id}
            type="button"
            onClick={action.onClick}
            disabled={action.disabled}
            className={`reminder-card__action reminder-card__action--${action.variant}`}
            aria-label={getActionAriaLabel(action.id as ReminderCardAction)}
          >
            {action.icon && (
              <span className="reminder-card__action-icon">{action.icon}</span>
            )}
            <span className="reminder-card__action-label">{action.label}</span>
          </button>
        ))}
        {renderSnoozeDropdown()}
      </div>
    );
  };

  const renderError = () => {
    if (!error) return null;

    return (
      <div className="reminder-card__error" role="alert">
        <span className="reminder-card__error-message">{error}</span>
        <button
          type="button"
          onClick={clearError}
          className="reminder-card__error-dismiss"
          aria-label="Dismiss error"
        >
          ✕
        </button>
      </div>
    );
  };

  const renderLoadingOverlay = () => {
    if (!isLoading) return null;

    return (
      <div className="reminder-card__loading-overlay">
        <div className="reminder-card__loading-spinner"></div>
        <span className="reminder-card__loading-message">
          {REMINDER_CARD_MESSAGES.LOADING}
        </span>
      </div>
    );
  };

  const getActionAriaLabel = (actionId: ReminderCardAction): string => {
    switch (actionId) {
      case 'mark_sent':
        return REMINDER_CARD_MESSAGES.ARIA_MARK_SENT;
      case 'snooze':
        return REMINDER_CARD_MESSAGES.ARIA_SNOOZE;
      case 'edit':
        return REMINDER_CARD_MESSAGES.ARIA_EDIT;
      case 'delete':
        return REMINDER_CARD_MESSAGES.ARIA_DELETE;
      case 'show_checklist':
        return REMINDER_CARD_MESSAGES.ARIA_CHECKLIST;
      default:
        return action.label;
    }
  };

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <div
      className={getCardClassName()}
      role="article"
      aria-label={`Reminder for ${event.contact_name} on ${displayData.displayDate}`}
    >
      {renderHeader()}
      {renderDetails()}
      {renderError()}
      {renderActions()}
      {renderLoadingOverlay()}

      {/* Expand/Collapse Button */}
      <button
        type="button"
        onClick={toggleExpanded}
        className="reminder-card__expand-button"
        aria-label={state.isExpanded
          ? REMINDER_CARD_MESSAGES.ARIA_COLLAPSE
          : REMINDER_CARD_MESSAGES.ARIA_EXPAND
        }
        aria-expanded={state.isExpanded ? 'true' : 'false'}
      >
        {state.isExpanded ? '▲' : '▼'}
      </button>
    </div>
  );
};
