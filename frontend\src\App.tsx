/**
 * Main App Component
 * Integrates all components with state management and routing
 *
 * Features:
 * - Component communication and data flow
 * - State management for selected events and checklists
 * - Error handling and loading states
 * - Responsive layout and navigation
 * - API integration and data synchronization
 */

import React, { useState, useCallback, useEffect } from 'react';
import { ImportForm } from './components/ImportForm';
import { EventList } from './components/EventList';
import { ReminderCard } from './components/ReminderCard';
import { Checklist } from './components/Checklist';
import { importContacts, scheduleReminders } from './services/api';
import { IContact, IContactData } from './types/api';
import './App.css';

// ============================================================================
// APP STATE INTERFACES
// ============================================================================

interface IAppState {
  /** Currently selected event for detailed view */
  selectedEvent: IContact | null;

  /** Whether checklist is visible */
  showChecklist: boolean;

  /** Current view mode */
  currentView: 'list' | 'detail' | 'import';

  /** Global loading state */
  isLoading: boolean;

  /** Global error message */
  error: string | null;

  /** Whether to refresh event list */
  refreshEvents: boolean;
}

const DEFAULT_APP_STATE: IAppState = {
  selectedEvent: null,
  showChecklist: false,
  currentView: 'list',
  isLoading: false,
  error: null,
  refreshEvents: false,
};

// ============================================================================
// MAIN APP COMPONENT
// ============================================================================

function App() {
  const [appState, setAppState] = useState<IAppState>(DEFAULT_APP_STATE);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleImportContact = useCallback(async (contactData: IContactData) => {
    setAppState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      await importContacts([contactData]);

      // Refresh event list after successful import
      setAppState(prev => ({
        ...prev,
        isLoading: false,
        refreshEvents: !prev.refreshEvents,
        currentView: 'list',
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to import contact';
      setAppState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  const handleEventSelect = useCallback((event: IContact) => {
    setAppState(prev => ({
      ...prev,
      selectedEvent: event,
      currentView: 'detail',
      showChecklist: false,
    }));
  }, []);

  const handleShowChecklist = useCallback((eventId: string) => {
    setAppState(prev => ({
      ...prev,
      showChecklist: true,
    }));
  }, []);

  const handleReminderSent = useCallback(async (eventId: string) => {
    setAppState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Implement API call to mark reminder as sent
      // For now, we'll just refresh the event list
      await scheduleReminders();

      setAppState(prev => ({
        ...prev,
        isLoading: false,
        refreshEvents: !prev.refreshEvents,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to mark reminder as sent';
      setAppState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  const handleSnooze = useCallback(async (eventId: string, snoozeHours: number) => {
    setAppState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Implement API call to snooze reminder
      // For now, we'll just show a success message
      console.log(`Snoozed reminder ${eventId} for ${snoozeHours} hours`);

      setAppState(prev => ({
        ...prev,
        isLoading: false,
        refreshEvents: !prev.refreshEvents,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to snooze reminder';
      setAppState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  const handleEditEvent = useCallback((event: IContact) => {
    // TODO: Implement edit functionality
    console.log('Edit event:', event);
    setAppState(prev => ({ ...prev, currentView: 'import' }));
  }, []);

  const handleDeleteEvent = useCallback(async (eventId: string) => {
    setAppState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // TODO: Implement API call to delete event
      console.log('Delete event:', eventId);

      setAppState(prev => ({
        ...prev,
        isLoading: false,
        refreshEvents: !prev.refreshEvents,
        selectedEvent: prev.selectedEvent?.id === eventId ? null : prev.selectedEvent,
        currentView: prev.selectedEvent?.id === eventId ? 'list' : prev.currentView,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete event';
      setAppState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, []);

  const handleNavigateToView = useCallback((view: IAppState['currentView']) => {
    setAppState(prev => ({
      ...prev,
      currentView: view,
      selectedEvent: view === 'list' ? null : prev.selectedEvent,
      showChecklist: false,
      error: null,
    }));
  }, []);

  const handleClearError = useCallback(() => {
    setAppState(prev => ({ ...prev, error: null }));
  }, []);

  const handleRefreshEvents = useCallback(() => {
    setAppState(prev => ({ ...prev, refreshEvents: !prev.refreshEvents }));
  }, []);

  // ============================================================================
  // RENDER HELPERS
  // ============================================================================

  const renderNavigation = () => (
    <nav className="app__navigation" role="navigation" aria-label="Main navigation">
      <div className="app__nav-brand">
        <h1 className="app__title">Reminder & Gifting</h1>
        <p className="app__subtitle">Never miss an important date</p>
      </div>

      <div className="app__nav-buttons">
        <button
          type="button"
          onClick={() => handleNavigateToView('list')}
          className={`app__nav-button ${appState.currentView === 'list' ? 'app__nav-button--active' : ''}`}
          aria-current={appState.currentView === 'list' ? 'page' : undefined}
        >
          📅 Events
        </button>

        <button
          type="button"
          onClick={() => handleNavigateToView('import')}
          className={`app__nav-button ${appState.currentView === 'import' ? 'app__nav-button--active' : ''}`}
          aria-current={appState.currentView === 'import' ? 'page' : undefined}
        >
          ➕ Add Contact
        </button>

        {appState.selectedEvent && (
          <button
            type="button"
            onClick={() => handleNavigateToView('detail')}
            className={`app__nav-button ${appState.currentView === 'detail' ? 'app__nav-button--active' : ''}`}
            aria-current={appState.currentView === 'detail' ? 'page' : undefined}
          >
            📋 Details
          </button>
        )}
      </div>
    </nav>
  );

  const renderGlobalError = () => {
    if (!appState.error) return null;

    return (
      <div className="app__error" role="alert">
        <div className="app__error-content">
          <span className="app__error-icon">⚠️</span>
          <span className="app__error-message">{appState.error}</span>
          <button
            type="button"
            onClick={handleClearError}
            className="app__error-dismiss"
            aria-label="Dismiss error"
          >
            ✕
          </button>
        </div>
      </div>
    );
  };

  const renderGlobalLoading = () => {
    if (!appState.isLoading) return null;

    return (
      <div className="app__loading-overlay">
        <div className="app__loading-content">
          <div className="app__loading-spinner"></div>
          <span className="app__loading-message">Processing...</span>
        </div>
      </div>
    );
  };

  const renderImportView = () => (
    <div className="app__view app__view--import">
      <div className="app__view-header">
        <h2 className="app__view-title">Add New Contact</h2>
        <p className="app__view-description">
          Enter contact details to set up a new reminder
        </p>
      </div>

      <div className="app__view-content">
        <ImportForm
          onSubmit={handleImportContact}
          onSuccess={() => {
            console.log('Contact imported successfully');
          }}
          onError={(error) => {
            setAppState(prev => ({ ...prev, error }));
          }}
          isLoading={appState.isLoading}
          resetOnSuccess={true}
          showSuccessMessage={true}
        />
      </div>
    </div>
  );

  const renderEventListView = () => (
    <div className="app__view app__view--list">
      <div className="app__view-header">
        <h2 className="app__view-title">Upcoming Events</h2>
        <p className="app__view-description">
          Manage your reminders and gift planning
        </p>
      </div>

      <div className="app__view-content">
        <EventList
          onEventSelect={handleEventSelect}
          onRefresh={handleRefreshEvents}
          isLoading={appState.isLoading}
          error={appState.error}
          showRefreshButton={true}
          showControls={true}
        />
      </div>
    </div>
  );

  const renderDetailView = () => {
    if (!appState.selectedEvent) {
      return (
        <div className="app__view app__view--detail">
          <div className="app__view-header">
            <h2 className="app__view-title">No Event Selected</h2>
            <p className="app__view-description">
              Select an event from the list to view details
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="app__view app__view--detail">
        <div className="app__view-header">
          <h2 className="app__view-title">Event Details</h2>
          <p className="app__view-description">
            Manage reminder and gift planning for {appState.selectedEvent.contact_name}
          </p>
        </div>

        <div className="app__view-content">
          <div className="app__detail-layout">
            <div className="app__detail-main">
              <ReminderCard
                event={appState.selectedEvent}
                onReminderSent={handleReminderSent}
                onSnooze={handleSnooze}
                onEdit={handleEditEvent}
                onDelete={handleDeleteEvent}
                onShowChecklist={handleShowChecklist}
                isLoading={appState.isLoading}
                error={appState.error}
                showActions={true}
                showChecklistButton={true}
                showDetails={true}
              />
            </div>

            {appState.showChecklist && (
              <div className="app__detail-sidebar">
                <Checklist
                  eventId={appState.selectedEvent.id}
                  onItemsChange={(items) => {
                    console.log('Checklist items updated:', items);
                  }}
                  onItemAdd={(item) => {
                    console.log('Checklist item added:', item);
                  }}
                  onItemUpdate={(item) => {
                    console.log('Checklist item updated:', item);
                  }}
                  onItemDelete={(itemId) => {
                    console.log('Checklist item deleted:', itemId);
                  }}
                  isLoading={appState.isLoading}
                  error={appState.error}
                  showAddForm={true}
                  allowInlineEdit={true}
                  allowDelete={true}
                  showCompleted={true}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderCurrentView = () => {
    switch (appState.currentView) {
      case 'import':
        return renderImportView();
      case 'detail':
        return renderDetailView();
      case 'list':
      default:
        return renderEventListView();
    }
  };

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    // Clear error after 5 seconds
    if (appState.error) {
      const timer = setTimeout(() => {
        handleClearError();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [appState.error, handleClearError]);

  // ============================================================================
  // MAIN RENDER
  // ============================================================================

  return (
    <div className="app" role="application">
      {renderNavigation()}
      {renderGlobalError()}

      <main className="app__main" role="main">
        {renderCurrentView()}
      </main>

      {renderGlobalLoading()}
    </div>
  );
}

export default App;
